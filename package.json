{"name": "pianorhythm-docs", "version": "0.0.1", "private": true, "scripts": {"docusaurus": "<PERSON>cusaurus", "start": "docusaurus start --port 4000 --hot-only", "build": "docusaurus build --out-dir dist", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "clear": "docusaurus clear", "serve": "docusaurus serve", "parse": "docusaurus parse", "glossary": "docusaurus glossary", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids", "generate-algolia": "node generate-algolia-records.js"}, "dependencies": {"@docusaurus-terminology/parser": "^1.3.0", "@docusaurus-terminology/term": "^1.0.0", "@docusaurus/core": "3.8.1", "@docusaurus/faster": "^3.8.1", "@docusaurus/plugin-client-redirects": "3.8.1", "@docusaurus/plugin-content-blog": "^3.8.1", "@docusaurus/plugin-content-docs": "3.8.1", "@docusaurus/plugin-google-gtag": "3.8.1", "@docusaurus/plugin-pwa": "3.8.1", "@docusaurus/preset-classic": "3.8.1", "@docusaurus/theme-classic": "3.8.1", "@docusaurus/theme-common": "3.8.1", "@docusaurus/theme-live-codeblock": "3.8.1", "@docusaurus/theme-mermaid": "^3.8.1", "@docusaurus/utils": "3.8.1", "@docusaurus/utils-common": "3.8.1", "@grnet/docusaurus-glossary-view": "1.0.0-rc.2", "@grnet/docusaurus-term-preview": "1.0.0-rc.1", "@grnet/docusaurus-terminology": "1.0.0-rc.2", "@mdx-js/react": "^3.1.0", "clsx": "^2.1.1", "docusaurus-lunr-search": "3.6.0", "prism-react-renderer": "^2.4.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-photo-album": "^3.0.2", "semver-sort": "^1.0.0", "styled-components": "6.1.8", "yet-another-react-lightbox": "^3.19.0"}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 3 chrome version", "last 3 firefox version", "last 3 safari version"]}, "engines": {"node": ">=19.0"}, "devDependencies": {"@docusaurus/module-type-aliases": "3.8.1", "@docusaurus/tsconfig": "3.8.1", "@docusaurus/types": "^3.8.1", "@types/react": "^19.0.7", "autocomplete.js": "^0.38.1", "autoprefixer": "^10.4.16", "classnames": "^2.3.2", "cross-env": "^7.0.3", "docusaurus-plugin-typedoc": "^0.19.2", "glob": "^11.0.3", "gray-matter": "^4.0.3", "hogan.js": "^3.0.2", "lunr": "^2.3.9", "mkdirp": "^3.0.1", "postcss": "^8.4.31", "raw-loader": "^4.0.2", "react-is": "^17.0.2", "remove-markdown": "^0.6.2", "tailwindcss": "^3.3.3", "typescript": "~5.8.3"}, "packageManager": "pnpm@9.4.0"}