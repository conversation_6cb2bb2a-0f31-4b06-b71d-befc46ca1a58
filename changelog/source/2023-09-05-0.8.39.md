---
  date: 2023-09-05
  version: 0.8.39
  tags:
    - 0.8.39
    - changelog
  
---

# 0.8.39

## :rocket: New Features
  - **Added 2D Mode** ([PRFP-1082](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1082)) After much contemplation, I've decided to add support back for a 2D renderer of the piano. You can toggle between 2D/3D (without having to restart) by clicking on the button that says `Camera Mode` in the button groups at the top right. _Note: Certain features such as the Midi Player are not available in 2d mode, until further notice._

{/* truncate */}

## :smile: Enhancements
  - ([PRFP-1094](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1094)) Added room option for enabling/disabling bots in the room.
  - Backend server refactoring.

## :bug: Bug Fixes
  - ([PRFP-1099](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1099)) Fixed issue with feedback loop with midi i/o.
  - ([PRFP-1098](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1098)) Fixed volume icons not updating the user volume slider.
  - Minor bug fixes.

<!----------------------------------------------->
