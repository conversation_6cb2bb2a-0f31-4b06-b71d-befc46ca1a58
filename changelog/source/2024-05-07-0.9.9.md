---
  date: 2024-05-07
  version: 0.9.9
  tags:
    - 0.9.9
    - changelog
  
---

# 0.9.9

## :rocket: New Features
  - **VirtualPiano Sheet Music Player** ([PRFP-463](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-463)) Added a basic virtual piano sheet music player! Find out more here: [VirtualPiano Sheet Music Player](/guides/midi-player/vp-sequencer/)
  <img src="/img/guide/midi-player/461e41788b0be39c6df1407996ec3419.gif" style={{marginLeft: '30px'}} alt="vp-sequencer" width="500"/>

{/* truncate */}

  - **Guests Can View Sheet Music Repo** Guest can now view the sheet music repository. They can't upload sheet music, but they can view the list of sheet music available. You can find it at `Tools -> Open Sheet Music Repo` or the `Sheet Music` button on the bottom bar.
  <img src="/img/changelogs/7c8e3f01886679528e60312795b43ae7.gif" style={{marginLeft: '30px'}} alt="sheet music repo" width="500"/>

  - ([PRFP-1146](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1146)) Added more general analytics/stats about the app. You can find it on the bottom bar as `Leaderboards`.
  <img src="/img/changelogs/81cf7c44ab8153a44091733bafa62c00.gif" style={{marginLeft: '30px'}} alt="graph stats" width="500"/>

  - ([PRFP-1221](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1221)) Added icons to represent the main sources of midi input by users: `PC Keyboard`, `Midi Piano`, and `Mouse`.
  <img src="/img/changelogs/fda5e71948308019d98f4346ba11f732.gif" style={{marginLeft: '30px'}} alt="note icons" width="200"/>

  - ([PRFP-1219](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1219)) Added a cookie consent form. You can find it at the bottom of the page. This is to comply with the EU's GDPR regulations.
  <img src="/img/changelogs/017e67675e7a434f0e307959ba89f007.png" style={{marginLeft: '30px'}} alt="cookie consent form" width="500"/>

  - ([PRFP-1068](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1068)) Added a search bar in many of the settings. Now it should be easier to find what you're looking for.
  <img src="/img/changelogs/aa7b05f9a4989b1c6d0f04ffc0cdf83f.gif" style={{marginLeft: '30px'}} alt="search bar" width="500"/>

  - ([PRFP-1223](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1223)) Added support for an extended layout for VP. You can find it at `Settings -> Input -> MIDI to VP/QWERTY Layout`.
  <img src="/img/changelogs/midi_to_qwerty_piano.png" style={{marginLeft: '30px'}} alt="search bar"/>

  - ([PRFP-1097](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1097)) Added keyboard mapping overlays for 3D and 2D. You can toggle by pressing `F6` or going to the action widgets on the right and pressing `Show Input Mapping`.

  - ([PRFP-1192](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1192)) Added a button to export logs for the desktop app. You can find it at `Settings -> Application -> Export Logs`.

## :smile: Enhancements
  - ([PRFP-936](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-936)) Reduced latency between keyboard input and sound output. The audio processing will use the main thread by default. The drawback is that it's more susceptible to instability spikes (may hear more static if there's too much load). You can toggle this in the settings by going to `Settings -> Soundfont -> Use AudioWorklet for audio processing`.
  - ([PRFP-1202](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1202)) The client has gone through a major refactor. This should help with performance and stability.
  - ([PRFP-1184](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1184)) Added a `Copy` button to the sheet music viewer.
  - ([PRFP-1207](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1207)) Added a progress bar when downloading soundfonts.
  - ([PRFP-1208](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1208)) Added a setting to toggle the audio equalizer.
  - ([PRFP-1213](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1213)) Added a new soundfount: `SGM-v2.01-NicePianosGuitarsBass-V1.2`
  - ([PRFP-1217](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1217)) Added a graphics setting for soft shadows.

## :bug: Bug Fixes
  - ([PRFP-1209](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1209)) Fixed issue with the 2D piano sometimes getting clipped on the edge of the window.
  - ([PRFP-1203](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1203)) Fixed an issue with server banning.
  - ([PRFP-1161](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1161)) Fixed the padding/spacing in the user profile "About" section text.
  - ([PRFP-741](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-741)) Fixed an issue with youtube links with parameters not getting parsed correctly.
  - ([PRFP-1204](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1204)) Fixed emails not being saved properly for new registrations.
  - ([PRFP-1215](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1215)) Fixed an issue with urls not being parsed correctly in chat due to server censorship.

<!----------------------------------------------->
