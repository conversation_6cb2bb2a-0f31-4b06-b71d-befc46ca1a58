name: 'deploy-docs'
on:
  push:
    branches: ["main"]
    paths:
      - "docs/**"
      - "blog/**"
      - "src/**"
      - "static/**"
      - "changelog/**"
      - "docusaurus.config.js"
      - "sidebars.js"
      - "package.json"
      - "pnpm-lock.yaml"

  workflow_dispatch:

concurrency:
  group: docs_deployment
  cancel-in-progress: true

jobs:
  build-docs:
    runs-on: [ubuntu-latest]
    steps:
      - uses: actions/checkout@v4

      - name: setup node
        uses: actions/setup-node@v4
        with:
          node-version: 19.2.0

      - name: 📥 PNPM install
        uses: ./.github/actions/pnpm-install

      - name: Generate build cache key
        id: build-cache-key
        run: |
          # Create a hash of all files that affect the build output
          CONTENT_HASH=$(find docs blog src static changelog docusaurus.config.js sidebars.js package.json pnpm-lock.yaml -type f -exec sha256sum {} \; 2>/dev/null | sort | sha256sum | cut -d' ' -f1)
          echo "hash=$CONTENT_HASH" >> $GITHUB_OUTPUT
          echo "Build content hash: $CONTENT_HASH"

      - name: Cache build output
        id: build-cache
        uses: actions/cache@v4
        with:
          path: ./dist
          key: ${{ runner.os }}-docs-build-${{ steps.build-cache-key.outputs.hash }}
          restore-keys: |
            ${{ runner.os }}-docs-build-

      - name: Build Docs
        if: steps.build-cache.outputs.cache-hit != 'true'
        run: pnpm run build

      - name: Verify build output exists
        run: |
          if [ ! -d "./dist" ]; then
            echo "Error: Build output directory ./dist does not exist"
            exit 1
          fi
          echo "Build output verified - dist directory exists"

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Install doctl
        uses: digitalocean/action-doctl@v2
        with:
          token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}

      - name: Publish Image to Container Registry
        uses: ripplr-io/docr-docker-publish@v1
        with:
          image_path: pianorhythm/docs
          dockerfile: docs.Dockerfile