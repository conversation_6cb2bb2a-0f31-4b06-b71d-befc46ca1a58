@tailwind base;
@tailwind components;
@tailwind utilities;

/**
 * Any CSS included here will be global. The classic template
 * bundles Infima by default. Infima is a CSS framework designed to
 * work well for content-centric websites.
 */

* {
  /*font-size: large;*/
}

/* You can override the default Infima variables here. */
:root {
  --ifm-color-primary: #363942;
  --ifm-color-primary-dark: #31333b;
  --ifm-color-primary-darker: #2e3038;
  --ifm-color-primary-darkest: #26282e;
  --ifm-color-primary-light: #3b3f49;
  --ifm-color-primary-lighter: #3e424c;
  --ifm-color-primary-lightest: #464a56;
  --ifm-code-font-size: 95%;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.1);
}

article {
  margin-left: 20px;
}

.docusaurus-highlight-code-line {
  background-color: rgba(0, 0, 0, 0.1);
  display: block;
  margin: 0 calc(-1 * var(--ifm-pre-padding));
  padding: 0 var(--ifm-pre-padding);
}

html[data-theme='dark'] {
  --ifm-navbar-background-color: #26282e;
  --ifm-footer-background-color: #26282e;
  --ifm-color-primary: #00d1b2;
  --ifm-color-primary-dark: #363942;
  --ifm-background-color: #26282e;
  --ifm-background-surface-color: #26282e;
  --ifm-code-background: #3b3f49;
  --ifm-navbar-shadow: 0px 1px 2px rgba(255,255,255,0.1);
  /* --ifm-color-content-secondary: white; */
}

html[data-theme='dark'] .hero--primary {
  --ifm-hero-background-color: var(--ifm-background-color);
  --ifm-hero-text-color: white;
}

::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  border-radius: 5px;
}

::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.4);
  box-shadow: inset 0 0 6px rgba(100, 100, 100, 0.5);
}

::-webkit-scrollbar-corner {
  background: transparent;
}

.footer--dark {
  --ifm-footer-background-color: #202127;
}

html[data-theme='dark'] .docusaurus-highlight-code-line {
  background-color: rgba(0, 0, 0, 0.3);
}

.base-image {
  border-radius: 10px;
  border: solid 3px white;
}

.base-video {
  border-radius: 10px;
  border: solid 3px white;
}

/* Tooltip container */
.tooltip {
  position: relative;
  display: inline-block;
  text-decoration: underline;
  /* font-style: italic; */
  cursor: default;
  border-bottom: 1px dotted black; /* If you want dots under the hoverable text */
}

.tooltip:hover {
}

/* Tooltip text */
.tooltip .tooltiptext {
  visibility: hidden;
  width: 120px;
  background-color: black;
  color: #fff;
  text-align: center;
  padding: 5px 0;
  border-radius: 6px;
  /* margin-left: 10px; */

  /* Position the tooltip text - see examples below! */
  position: absolute;
  margin-left: 10px;
  top: -5px;

  /* left: 0; */
  /* top: -40px; */
  z-index: 1;
}

/* Show the tooltip text when you mouse over the tooltip container */
.tooltip:hover .tooltiptext {
  visibility: visible;
}