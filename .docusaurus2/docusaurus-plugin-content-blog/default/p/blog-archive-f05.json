{"archive": {"blogPosts": [{"id": "back-from-hiatus", "metadata": {"permalink": "/blog/back-from-hiatus", "source": "@site/blog/2025-06-28-back-from-hiatus/index.md", "title": "I'm Back! PianoRhythm v0.10.0 Coming Soon", "description": "Hey everyone! <PERSON> here, and I'm excited to announce that I'm back to actively working on PianoRhythm after several months of hiatus.", "date": "2025-06-28T00:00:00.000Z", "tags": [{"inline": true, "label": "oak", "permalink": "/blog/tags/oak"}, {"inline": true, "label": "pianorhythm", "permalink": "/blog/tags/pianorhythm"}, {"inline": true, "label": "hiatus", "permalink": "/blog/tags/hiatus"}, {"inline": true, "label": "v0.10.0", "permalink": "/blog/tags/v-0-10-0"}, {"inline": true, "label": "comeback", "permalink": "/blog/tags/comeback"}, {"inline": true, "label": "staging", "permalink": "/blog/tags/staging"}], "readingTime": 2.6, "hasTruncateMarker": true, "authors": [{"name": "Oak", "title": "PianoRhythm Creator/Developer", "url": "<EMAIL>", "key": "oak", "page": null}], "frontMatter": {"slug": "back-from-hiatus", "title": "I'm Back! PianoRhythm v0.10.0 Coming Soon", "authors": ["oak"], "tags": ["oak", "pianorhythm", "hiatus", "v0.10.0", "comeback", "staging"]}, "unlisted": false, "nextItem": {"title": "Server Upgrade", "permalink": "/blog/update_2023"}}, "content": "Hey everyone! Oak here, and I'm excited to announce that I'm back to actively working on PianoRhythm after several months of hiatus.\n\nI know many of you have been wondering what's been going on, and I wanted to take a moment to update you all on where things stand and what's coming next.\n\n{/* truncate */}\n\n## Where I've Been\n\nLife has a way of throwing curveballs, and the past several months have been no exception for me. Between work commitments, personal matters, and the need to step back and recharge, I had to take some time away from active PianoRhythm development. \n\nI know this might have been frustrating for some of you who were eagerly waiting for updates, and I truly appreciate your patience and continued support during this time. The PianoRhythm community has always been incredible, and knowing that you've stuck around means the world to me.\n\n## What's Coming: v0.10.0\n\nI'm thrilled to share that I'm back in full development mode, and I have some exciting news: **PianoRhythm v0.10.0 is planned for release around August 2025!**\n\nThis upcoming version represents a significant milestone in PianoRhythm's journey. While I can't reveal all the details just yet, I can tell you that v0.10.0 will include:\n\n- (Potential) Performance improvements and bug fixes\n- Continued refinements to the audio engine\n- UI/UX improvements\n- Dedicated page for the sheet music repository\n- And more!\n\nI'll be sharing more specific details about the features and improvements as we get closer to the release date.\n\n## Check Out the Staging Site\n\nFor those of you who want to get a sneak peek at what's coming, you can check out the latest development version on our **staging site** at [https://staging.pianorhythm.io](https://staging.pianorhythm.io). This is where I test new features and improvements before they make it to the main application.\n\nKeep in mind that the staging site is for testing purposes, so you might encounter some bugs or incomplete features. But it's a great way to see the direction PianoRhythm is heading and provide feedback on new developments.\n\n## Moving Forward\n\nI'm committed to being more consistent with development and communication moving forward. The break, while necessary, has given me renewed energy and perspective on PianoRhythm's future.\n\nI'll be posting more regular updates here on the blog and staying more active in the community. Your feedback, suggestions, and bug reports are invaluable in making PianoRhythm the best it can be.\n\n## Thank You\n\nBefore I wrap up, I want to give a huge thank you to everyone who has continued to support PianoRhythm during my absence. Whether you've been playing regularly, sharing the app with friends, or just patiently waiting for updates - you're the reason I'm motivated to keep building and improving this platform.\n\nPianoRhythm has always been a passion project, and seeing how much it means to the community makes all the hard work worthwhile.\n\nStay tuned for more updates as we approach the v0.10.0 release. I'm excited to share this journey with all of you!\n\nAs always, you can reach me at:\n- Email: <EMAIL>\n- Discord: Feel free to ping me in the PianoRhythm Discord server\n\nLet's make some beautiful music together! 🎹\n\n— Oak"}, {"id": "update_2023", "metadata": {"permalink": "/blog/update_2023", "source": "@site/blog/2023-12-30/index.md", "title": "Server Upgrade", "description": "Whew, it's been a while since I've posted here. I've been busy with life and other things, but I've finally gotten around to upgrading the server. Version 0.9.0 has now been released! I've also added a few new features to the app. Let's dive in, shall we?", "date": "2023-12-30T00:00:00.000Z", "tags": [{"inline": true, "label": "oak", "permalink": "/blog/tags/oak"}, {"inline": true, "label": "pianorhythm", "permalink": "/blog/tags/pianorhythm"}, {"inline": true, "label": "server upgrade", "permalink": "/blog/tags/server-upgrade"}], "readingTime": 2.57, "hasTruncateMarker": true, "authors": [{"name": "Oak", "title": "PianoRhythm Creator/Developer", "url": "<EMAIL>", "key": "oak", "page": null}], "frontMatter": {"slug": "update_2023", "title": "Server Upgrade", "authors": ["oak"], "tags": ["oak", "pianorhythm", "server upgrade"]}, "unlisted": false, "prevItem": {"title": "I'm Back! PianoRhythm v0.10.0 Coming Soon", "permalink": "/blog/back-from-hiatus"}, "nextItem": {"title": "Welcome", "permalink": "/blog/welcome"}}, "content": "Whew, it's been a while since I've posted here. I've been busy with life and other things, but I've finally gotten around to upgrading the server. Version `0.9.0` has now been released! I've also added a few new features to the app. Let's dive in, shall we?\r\n\r\n{/* truncate */}\r\n\r\n### Server Upgrade\r\nI've been on quite an adventure with the development of the server. Initially, it was built using F# and the Akka.NET framework. However, as the complexity grew, I found the backend becoming a bit too challenging to maintain. Also, the server didn't seem to be as performant as I would have liked. So I hope many of the weird bugs that you've encountered in the past, due to the server, are now gone with this new update.\r\n\r\nSo, I decided to give Rust a try. Rust, with the Actix framework, offered a fresh start and a chance to streamline the codebase. I've been diving deep into Rust lately, and I've been enjoying every minute of it. The decision to rewrite the backend in Rust was a significant one, but it's a decision I'm glad I made.\r\n\r\nHowever, it's not a complete rewrite. There are still certain prior features that I need to rewrite. For example, the backend for the sheet music repo service needs to be ported into Rust. I hope to get that done in the following weeks.\r\n\r\nThe transition to Rust has not only made the backend easier to maintain but also more efficient at processing web requests. Plus, it's opened up a whole new world of possibilities for adding new features. I'm excited about the improvements I've made and even more excited about what's to come.\r\n\r\nStay tuned for more updates as I continue to enhance the app with new features!\r\n\r\n### New Features\r\n\r\nIn the recent development cycle, I was also able to add/improve some features.\r\n\r\n- Added two new stages: \"Music Studio\" and \"Arena.\"\r\n- Added stage sound effects. You can now play background music while playing the piano.\r\n  - Rain, Wind, and Bird audio effects.\r\n- Moderators are now able to add/remove badges to/from users. With that being said, I would like to add more moderators to the team. If you're interested, please send me a message or you can fill out this form: [Application Form](https://form.jotform.com/240056814147150).\r\n-  I've implemented a system to create a new lobby when a user is attempting to join a lobby that is full.\r\n\r\n### Future Plans\r\n\r\nWith the release of version `0.9.0`, we're reaching the near end of the beta phase. If all goes well with this new server upgradeg, then I plan on releasing version `1.0.0` in the coming months.\r\n\r\nThe major focuses for the next few months will be:\r\n- Improving the UI/UX.\r\n- Add a Midi Music repository.\r\n- Adding more social features.\r\n- Adding more exclusive features for the PRO subscription plan.\r\n- Stabilize the self-hosting process.\r\n- Improving mobile support.\r\n- Improving localization support _(Send me a message if you would like to contribute. You'll get a translator badge as recognizition for your efforts)_.\r\n- Add more DAW like features.\r\n- Bring back and improve Avatars."}, {"id": "welcome", "metadata": {"permalink": "/blog/welcome", "source": "@site/blog/2023-05-02-welcome/index.md", "title": "Welcome", "description": "Welcome to PianoRhythm's first blog post!", "date": "2023-05-02T00:00:00.000Z", "tags": [{"inline": true, "label": "welcome", "permalink": "/blog/tags/welcome"}, {"inline": true, "label": "oak", "permalink": "/blog/tags/oak"}, {"inline": true, "label": "pianorhythm", "permalink": "/blog/tags/pianorhythm"}, {"inline": true, "label": "history", "permalink": "/blog/tags/history"}, {"inline": true, "label": "v2", "permalink": "/blog/tags/v-2"}, {"inline": true, "label": "v2 history", "permalink": "/blog/tags/v-2-history"}, {"inline": true, "label": "v3", "permalink": "/blog/tags/v-3"}], "readingTime": 9.24, "hasTruncateMarker": true, "authors": [{"name": "Oak", "title": "PianoRhythm Creator/Developer", "url": "<EMAIL>", "key": "oak", "page": null}], "frontMatter": {"slug": "welcome", "title": "Welcome", "authors": ["oak"], "tags": ["welcome", "oak", "pianorhythm", "history", "v2", "v2 history", "v3"]}, "unlisted": false, "prevItem": {"title": "Server Upgrade", "permalink": "/blog/update_2023"}}, "content": "Welcome to PianoRhythm's first blog post!\n\nYes, it is <PERSON>, <PERSON>! I am the creator and main developer of PianoRhythm.\n\nIf you're looking to create a blog post pertaining to PianoRhythm, piano, or just anything music related, then feel free to contact me!\n\n- Email: <EMAIL>\n- Discord: Oak#9806\n\nYou can find the PianoRhythm app's url [here](https://pianorhythm.io).\n\n{/* truncate */}\n\n### Why did I create PianoRhythm in the first place?\nWell, like many of you, I was an avid user of MPP and loved it. At the time, I just got into learning the piano and actual programming. I thought MPP had so much potential and the lack of updates kind of pushed me into envisioning my own thing. PianoRhythm is just a passion product. I didn't create it for the profit or for the masses.\n\nIt's something I've been putting my own time and money cause I just really enjoy creating and didn't mind sharing the platform with other people. I'm not trying to compete with MPP. I wanted to PianoRhythm to be its own thing with a unique world. I'm still striving for that. So far, I've been the sole developer with some help from Bop<PERSON><PERSON> on the Discord stuff in the backend. I can only do so much as a working professional.\n\n### What happened to v2?\nSo v2 was created back in 2016 when I just started learning programming in college. I definitely used MPP as the basis for the idea but used that as an opportunity to try different things. v2 was generally stable because it was simpler and less demanding. And that's because I was initially following MPP's footsteps. But for those OG members who may remember, that I did have a 3D mode in v2. However, what was the common complaint? That it was too laggy for lower performing machines.\n\nSo, I scrapped that idea in the mean time to focus. But, I really really preferred the aesthetics of 3D. The way I justify it is that if you really want 2d, then you have MPP for that. May not be ideal but there's nothing stopping you from using both (free) services.\n\nI wanted PR to be different. What's the point of creating a clone of MPP with just a different skin? The features that were in v2 were ideas I just wanted to play around with that I thought people would use. So, like being able to record, have multiple instruments, have a personal representation with avatars (blobs), and other stuff. I also wanted to make PR more game like because I've always been interested in making games as well. v2 was always in early alpha state since there was a lot of active development. But the ideas that I had in mind were still limited by certain technological limits that I'll talk about later.\n\nAnyhoo, I was in college back then and my time was limited. Since I was still learning of heck of a lot of stuff about development, there was a lot of sphaghetti code and probably bad practices. Add also that here were plenty of hiatuses that occurred and over time, things became unmaintable.\nOnce I got an internship at my current workplace, I learned a lot of new stuff and decided to create the project from scratch with my new knowledge.\nThus came v3. The eventual plan was to get v3 to decent enough state with similar features to v2 and have it replace it. Before I officially replaced v2, I did have message that would show in v2's lobby about me working on v3 so hopefully it wasn't that much of a surprise. And that was there for at least a few months.\n\n### What's up with v3?\nv3 has an interesting history because I've probably rewritten it from scratch like a dozen times. But before I get to some of the history, I'll first answer a few common things people have said:\n\n#### Why is V3 like forced 3D? It's so laggy!\nLike I mentioned above, that is the direction I've wanted to take and I knew it was going to outcast some users. It's impossible to please everyone so I'm not trying to. I'm still learning so I will keep trying to optimizing the app as best as I can but there's only so much old hardware can deal with. My machine is not a beast (GTX 1080 GPU and i7 CPU) but it runs perfectly smooth so far. Unfortunately, it's not like I have a bunch of old laptops sitting around where I can do performance tests. Keep in mind that v3 is in open beta and in active development. This where you guys come in and provide feedback so I can best try and deal with these issues. In general, v3 has a totally different tech stack from v2. Why? Cause I want to learn and try different things. If you've created any software, you would know how rapid frameworks, libraries, and other tech can get.\n\nHonestly, I would've like to use a proper 3D game engine to make PianoRhythm but for now, web developing is relatively easier and has better cross platform compatibility. I'm primarily limited to web technology. The desktop app (and no, it's actually not using Electron but actually using a product called Tauri (https://tauri.app/) that renders using the machine's native webview), was an attempt to at least provide more stability and a better desktop app experience.\n\n#### What's up v3's audio engine? It's terrible.\nHmm, that's primarily subjective in my opinion. So, I'll cite some sources and the reasoning behind certain things with the audio engine.\nI knew people were going to probably bash v3's audio since they were most likely used to v2's audio. **A lot of people don't like change**. Sure, there's probably a distinctive difference in audio fidelity but I imagine if you never used v2 in the first place and came to v3, then you would've probably had a different view.\n\nLuckily, if you don't like v3's audio, you can always use the midi output to play audio on your preferred synthesizer of choice. And once again, this is in active development where there's always room for improvement. If you're a developer that can build a better audio engine, then please let me know. I'm just one person working on the front and back end.\n\nFirst, I wanted PR to be able to use multiple instruments and thus allowing other users to hear such instruments to have a band/orchestra like experience. MPO (multiplayer orchestra) pretty much tackled that.\n\nv2's audio engine was using an existing tech that converted audio samples to a base64 encoded javascript object and allowed to process those encoded samples through WebAudio (https://github.com/gleitz/midi-js-soundfonts).\nSo an example would like look:\n\n```js\nMIDI.Soundfont.high_quality_acoustic_grand_piano = {\n    \"A0\": \"data:audio/ogg;base64,SUQzAwAAAAAAOlRYWFgAAAAW...\"\n}\n```\n\nWas it the best choice back then? Who knows? I was still learning\nand it was relatively easy to use. However, these objects were per instrument and took a bit of computer memory. So, each instrument had to manually be loaded into the browser to be able to be played. Okay so what's the issue? Well, if I was going to provide a feature to allow using different instruments, most people would expect to able to hear other users' instruments. That would be fine and dandy if it was just a few instruments but these audio js objects were converted from .sf2 soundfonts. These are files that are sample based audio files that can contain multiple instruments and sound effects. These have been standardized and a GM (General Midi) usually contain at least 100+ instruments.\n\nI recall that I experimented trying to load every single JavaScript audio object and quickly ran into memory limitation issues (like 2GB used for the audio) with Chrome. Often times, the browsers limits what an active site can do. So from a design aspect, I couldn't really justify users kind of forcing other users to load these audio objects with how much it cost.\n\nI had to think of an alternative and that's where using soundfont files (.sf2) came to mind. Theoretically, the tech behind them seem to be effecient and really what I was looking for. A 10mb soundfont file could potentially have all the GM instruments loaded without costing a lot in RAM. Also, there would be added benefit of people using their own custom soundfonts if they didn't like the default ones that PR might have. Win/Win, right? At the time of v2, I don't recall of any stable libraries that supported loading sf2 files.\n\nI'm no audio engineer so I definitely was going to try and build one from scratch since it was way out of my scope. But, I didn't give up. That's why with v3, I did so many rewrites trying a lot of different things. Developing is hard. I'm not a genius. Just an average programmer that's trying his best.\n\nOnce better technology came about, I finally decided to go the Web WASM route and found a SF2 parsing library written in Rust called OxiSynth (https://github.com/PolyMeilex/OxiSynth). This was also an opportunity to learn a new programming language (developers like shiny new stuff).\n\nSo far, I like this library. Since I didn't write it from scratch, I don't know the ins and outs, yet. There's a lot of low level audio programming that I have to get familiar with and that will take time.  Now if users have the same soundfont, you can load any instrument and have it heard by anyone without any additional cost.\n\nUsers also now have an option to use higher quality soundfonts without any major real drawback (their machine is the only limitation, I suppose). I still have to figure out how to allow users to hear a custom soundfont that someone else loaded (I do not want to force someone to have to download a 2gb soundfont for example). And speaking of size, choosing a default soundfont was a compromise. Sure, I could have a high quality one as the default but the default but that would incur a large download. So, I initially settled on a standard GM soundfont that was around 50-60mb that sounded decent (at least to me). However, it was recently pushed to like 100ish since people really wanted the old v2 default piano (which is about 50+ mb decoded in audio samples). So, I learned to created soundfonts and replaced the first piano in the default with v2's.\n\nI recently added a first draft of an equalizer for those who want to try and fine tune the audio. The point is that I'm trying my best but I can't please everyone. So, if you have supported me thus far, I really appreciate it. There's been plenty of times where I've wanted to quit and just completely abandon the project. As a creator, open criticism is expected and I can deal with. But I can definitely do without people who are just extremely negative with nothing to offer."}]}}