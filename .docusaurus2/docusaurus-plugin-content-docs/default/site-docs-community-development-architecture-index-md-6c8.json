{"id": "community/development/architecture/dev-architecture", "title": "Architecture", "description": "This part is to show some of the internal architecture of PianoRhythm's client (frontend) and server (backend).", "source": "@site/docs/community/development/architecture/index.md", "sourceDirName": "community/development/architecture", "slug": "/community/development/architecture/", "permalink": "/community/development/architecture/", "draft": false, "unlisted": false, "tags": [{"inline": true, "label": "architecture", "permalink": "/tags/architecture"}], "version": "current", "lastUpdatedBy": "Oak", "lastUpdatedAt": 1737050510000, "frontMatter": {"title": "Architecture", "id": "dev-architecture", "path": ["/community/development/architecture/"], "tags": ["architecture"]}, "sidebar": "communitySidebar", "previous": {"title": "Development", "permalink": "/community/development/"}, "next": {"title": "Client", "permalink": "/community/development/architecture/client/"}}