{"id": "api/enums/types_user_types.StatusColor", "title": "Enumeration: StatusColor", "description": "types/user.types.StatusColor", "source": "@site/docs/api/enums/types_user_types.StatusColor.md", "sourceDirName": "api/enums", "slug": "/api/enums/types_user_types.StatusColor", "permalink": "/api/enums/types_user_types.StatusColor", "draft": false, "unlisted": false, "editUrl": null, "tags": [], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055, "formattedLastUpdatedAt": "Oct 14, 2018", "frontMatter": {"id": "types_user_types.StatusColor", "title": "Enumeration: StatusColor", "sidebar_label": "StatusColor", "custom_edit_url": null}}