{"id": "community/development/architecture/client/architecture-client", "title": "Client", "description": "---", "source": "@site/docs/community/development/architecture/client/index.md", "sourceDirName": "community/development/architecture/client", "slug": "/community/development/architecture/client/", "permalink": "/community/development/architecture/client/", "draft": false, "unlisted": false, "tags": [{"inline": true, "label": "architecture", "permalink": "/tags/architecture"}, {"inline": true, "label": "client", "permalink": "/tags/client"}, {"inline": true, "label": "frontend", "permalink": "/tags/frontend"}, {"inline": true, "label": "desktop", "permalink": "/tags/desktop"}, {"inline": true, "label": "web development", "permalink": "/tags/web-development"}, {"inline": true, "label": "JavaScript", "permalink": "/tags/java-script"}, {"inline": true, "label": "Rust", "permalink": "/tags/rust"}], "version": "current", "lastUpdatedBy": "Oak", "lastUpdatedAt": 1738148804000, "frontMatter": {"title": "Client", "id": "architecture-client", "path": ["/development/architecture/client"], "tags": ["architecture", "client", "frontend", "desktop", "web development", "JavaScript", "Rust"]}, "sidebar": "communitySidebar", "previous": {"title": "Architecture", "permalink": "/community/development/architecture/"}, "next": {"title": "Gallery", "permalink": "/community/development/gallery/"}}