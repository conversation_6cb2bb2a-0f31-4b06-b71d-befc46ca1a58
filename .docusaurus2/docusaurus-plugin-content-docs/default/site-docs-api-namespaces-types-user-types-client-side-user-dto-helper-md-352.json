{"id": "api/namespaces/types_user_types.ClientSideUserDtoHelper", "title": "Namespace: ClientSideUserDtoHelper", "description": "types/user.types.ClientSideUserDtoHelper", "source": "@site/docs/api/namespaces/types_user_types.ClientSideUserDtoHelper.md", "sourceDirName": "api/namespaces", "slug": "/api/namespaces/types_user_types.ClientSideUserDtoHelper", "permalink": "/api/namespaces/types_user_types.ClientSideUserDtoHelper", "draft": false, "unlisted": false, "editUrl": null, "tags": [], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055, "formattedLastUpdatedAt": "Oct 14, 2018", "frontMatter": {"id": "types_user_types.ClientSideUserDtoHelper", "title": "Namespace: ClientSideUserDtoHelper", "sidebar_label": "ClientSideUserDtoHelper", "custom_edit_url": null}}