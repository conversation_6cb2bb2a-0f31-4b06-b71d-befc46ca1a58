{"id": "api/classes/plugins_plugin_api.IPianoRhythmPluginAPI", "title": "Class: IPianoRhythmPluginAPI", "description": "plugins/plugin-api.IPianoRhythmPluginAPI", "source": "@site/docs/api/classes/plugins_plugin_api.IPianoRhythmPluginAPI.md", "sourceDirName": "api/classes", "slug": "/api/classes/plugins_plugin_api.IPianoRhythmPluginAPI", "permalink": "/api/classes/plugins_plugin_api.IPianoRhythmPluginAPI", "draft": false, "unlisted": false, "editUrl": null, "tags": [], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055, "formattedLastUpdatedAt": "Oct 14, 2018", "frontMatter": {"id": "plugins_plugin_api.IPianoRhythmPluginAPI", "title": "Class: IPianoRhythmPluginAPI", "sidebar_label": "IPianoRhythmPluginAPI", "custom_edit_url": null}, "sidebar": "docsSideBar", "previous": {"title": "Table of Contents", "permalink": "/api/modules"}, "next": {"title": "Main", "permalink": "/api/classes/plugins_plugin_template.Main"}}