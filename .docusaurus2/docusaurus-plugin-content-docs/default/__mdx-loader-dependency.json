{"options": {"routeBasePath": "/", "sidebarPath": "C:\\Projects\\pianorhythm-docs\\sidebars.js", "showLastUpdateAuthor": true, "showLastUpdateTime": true, "includeCurrentVersion": true, "exclude": ["./api/index.md"], "path": "docs", "editCurrentVersion": false, "editLocalizedFiles": false, "tagsBasePath": "tags", "include": ["**/*.{md,mdx}"], "sidebarCollapsible": true, "sidebarCollapsed": true, "docsRootComponent": "@theme/DocsRoot", "docVersionRootComponent": "@theme/DocVersionRoot", "docRootComponent": "@theme/DocRoot", "docItemComponent": "@theme/DocItem", "docTagsListComponent": "@theme/DocTagsListPage", "docTagDocListComponent": "@theme/DocTagDocListPage", "docCategoryGeneratedIndexComponent": "@theme/DocCategoryGeneratedIndexPage", "remarkPlugins": [], "rehypePlugins": [], "recmaPlugins": [], "beforeDefaultRemarkPlugins": [], "beforeDefaultRehypePlugins": [], "admonitions": true, "disableVersioning": false, "versions": {}, "breadcrumbs": true, "onInlineTags": "warn", "id": "default"}, "versionsMetadata": [{"versionName": "current", "label": "Next", "banner": null, "badge": false, "noIndex": false, "className": "docs-version-current", "path": "/", "tagsPath": "/tags", "isLast": true, "routePriority": -1, "sidebarFilePath": "C:\\Projects\\pianorhythm-docs\\sidebars.js", "contentPath": "C:\\Projects\\pianorhythm-docs\\docs", "contentPathLocalized": "C:\\Projects\\pianorhythm-docs\\i18n\\en\\docusaurus-plugin-content-docs\\current"}]}