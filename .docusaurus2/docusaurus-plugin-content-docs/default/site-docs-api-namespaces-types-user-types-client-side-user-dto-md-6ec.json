{"id": "api/namespaces/types_user_types.ClientSideUserDto", "title": "Namespace: ClientSideUserDto", "description": "types/user.types.ClientSideUserDto", "source": "@site/docs/api/namespaces/types_user_types.ClientSideUserDto.md", "sourceDirName": "api/namespaces", "slug": "/api/namespaces/types_user_types.ClientSideUserDto", "permalink": "/api/namespaces/types_user_types.ClientSideUserDto", "draft": false, "unlisted": false, "editUrl": null, "tags": [], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055, "formattedLastUpdatedAt": "Oct 14, 2018", "frontMatter": {"id": "types_user_types.ClientSideUserDto", "title": "Namespace: ClientSideUserDto", "sidebar_label": "ClientSideUserDto", "custom_edit_url": null}}