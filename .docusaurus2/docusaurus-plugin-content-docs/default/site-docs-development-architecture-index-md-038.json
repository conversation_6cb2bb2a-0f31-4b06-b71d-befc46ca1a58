{"id": "development/architecture/dev-architecture", "title": "Architecture", "description": "This part is to show some of the internal architecture of PianoRhythm's client (frontend) and server (backend).", "source": "@site/docs/development/architecture/index.md", "sourceDirName": "development/architecture", "slug": "/development/architecture/", "permalink": "/development/architecture/", "draft": false, "unlisted": false, "tags": [{"inline": true, "label": "architecture", "permalink": "/tags/architecture"}], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055000, "frontMatter": {"title": "Architecture", "id": "dev-architecture", "path": ["/development/architecture/"], "tags": ["architecture"]}, "sidebar": "developmentSidebar", "previous": {"title": "Development", "permalink": "/development/"}, "next": {"title": "Client", "permalink": "/development/architecture/client/"}}