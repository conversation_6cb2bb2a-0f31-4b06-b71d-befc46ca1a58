{"label": "architecture", "permalink": "/tags/architecture", "allTagsPath": "/tags", "count": 2, "items": [{"id": "development/architecture/dev-architecture", "title": "Architecture", "description": "This part is to show some of the internal architecture of PianoRhythm's client (frontend) and server (backend).", "permalink": "/development/architecture/"}, {"id": "development/architecture/client/architecture-client", "title": "Client", "description": "---", "permalink": "/development/architecture/client/"}], "unlisted": false}