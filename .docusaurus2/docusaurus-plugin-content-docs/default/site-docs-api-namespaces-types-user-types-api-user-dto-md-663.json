{"id": "api/namespaces/types_user_types.ApiUserDto", "title": "Namespace: ApiUserDto", "description": "types/user.types.ApiUserDto", "source": "@site/docs/api/namespaces/types_user_types.ApiUserDto.md", "sourceDirName": "api/namespaces", "slug": "/api/namespaces/types_user_types.ApiUserDto", "permalink": "/api/namespaces/types_user_types.ApiUserDto", "draft": false, "unlisted": false, "editUrl": null, "tags": [], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055, "formattedLastUpdatedAt": "Oct 14, 2018", "frontMatter": {"id": "types_user_types.ApiUserDto", "title": "Namespace: ApiUserDto", "sidebar_label": "ApiUserDto", "custom_edit_url": null}}