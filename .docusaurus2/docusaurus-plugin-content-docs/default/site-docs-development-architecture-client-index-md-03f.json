{"id": "development/architecture/client/architecture-client", "title": "Client", "description": "---", "source": "@site/docs/development/architecture/client/index.md", "sourceDirName": "development/architecture/client", "slug": "/development/architecture/client/", "permalink": "/development/architecture/client/", "draft": false, "unlisted": false, "tags": [{"inline": true, "label": "architecture", "permalink": "/tags/architecture"}, {"inline": true, "label": "client", "permalink": "/tags/client"}, {"inline": true, "label": "frontend", "permalink": "/tags/frontend"}, {"inline": true, "label": "desktop", "permalink": "/tags/desktop"}, {"inline": true, "label": "web development", "permalink": "/tags/web-development"}, {"inline": true, "label": "JavaScript", "permalink": "/tags/java-script"}, {"inline": true, "label": "Rust", "permalink": "/tags/rust"}], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055000, "frontMatter": {"title": "Client", "id": "architecture-client", "path": ["/development/architecture/client"], "tags": ["architecture", "client", "frontend", "desktop", "web development", "JavaScript", "Rust"]}, "sidebar": "developmentSidebar", "previous": {"title": "Architecture", "permalink": "/development/architecture/"}}