{"tag": {"label": "architecture", "permalink": "/tags/architecture", "allTagsPath": "/tags", "count": 2, "items": [{"id": "community/development/architecture/dev-architecture", "title": "Architecture", "description": "This part is to show some of the internal architecture of PianoRhythm's client (frontend) and server (backend).", "permalink": "/community/development/architecture/"}, {"id": "community/development/architecture/client/architecture-client", "title": "Client", "description": "---", "permalink": "/community/development/architecture/client/"}], "unlisted": false}}