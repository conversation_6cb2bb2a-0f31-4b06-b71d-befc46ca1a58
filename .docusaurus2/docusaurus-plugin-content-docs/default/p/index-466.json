{"version": {"pluginId": "default", "version": "current", "label": "Next", "banner": null, "badge": false, "noIndex": false, "className": "docs-version-current", "isLast": true, "docsSidebars": {"docsSideBar": [{"type": "category", "label": "Guides", "items": [{"type": "category", "label": "General", "items": [{"type": "link", "label": "Commands", "href": "/guides/general/commands", "docId": "guides/general/commands", "unlisted": false}], "collapsed": true, "collapsible": true, "href": "/guides/"}, {"type": "category", "label": "Rooms", "items": [{"type": "link", "label": "Creating Rooms", "href": "/guides/rooms/create-rooms", "docId": "guides/rooms/create-rooms", "unlisted": false}], "collapsed": true, "collapsible": true, "href": "/guides/rooms"}, {"type": "category", "label": "Instrument Dock", "items": [{"type": "link", "label": "Channel Parameters", "href": "/guides/instrument-dock/channel-parameters", "docId": "guides/instrument-dock/channel-parameters", "unlisted": false}], "collapsed": true, "collapsible": true, "href": "/guides/instrument-dock/"}, {"type": "link", "label": "Sheet Music", "href": "/guides/sheet-music/", "docId": "guides/sheet-music/sheetmusic", "unlisted": false}, {"type": "category", "label": "MIDI Player", "items": [{"type": "link", "label": "Virtual Piano Player", "href": "/guides/midi-player/vp-sequencer", "docId": "guides/midi-player/vp-sequencer", "unlisted": false}], "collapsed": true, "collapsible": true, "href": "/guides/midi-player/"}, {"type": "category", "label": "Audio Components", "items": [{"type": "link", "label": "<PERSON><PERSON>", "href": "/guides/components/looper", "docId": "guides/components/looper", "unlisted": false}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Orchestra Mode", "items": [{"type": "link", "label": "Piano Customization", "href": "/guides/customization/customization-piano", "docId": "guides/customization/customization-piano", "unlisted": false}], "collapsed": true, "collapsible": true, "href": "/guides/orchestra-mode/"}, {"type": "link", "label": "PRO Subscription", "href": "/guides/subscription/", "docId": "guides/subscription/pro-subscription", "unlisted": false}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Tutorials", "items": [{"type": "link", "label": "Getting Started", "href": "/tutorials/", "docId": "tutorials/tutorials", "unlisted": false}, {"type": "link", "label": "Changing Instruments", "href": "/tutorials/tutorial-instruments", "docId": "tutorials/tutorial-instruments", "unlisted": false}, {"type": "link", "label": "Loading Soundfonts", "href": "/tutorials/tutorial-soundfonts", "docId": "tutorials/tutorial-soundfonts", "unlisted": false}, {"type": "link", "label": "Self Hosted Rooms", "href": "/tutorials/tutorial-self-host-rooms", "docId": "tutorials/tutorial-self-host-rooms", "unlisted": false}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Advanced Tutorials", "items": [{"type": "link", "label": "Plugins", "href": "/advanced-guides/plugins/", "docId": "advanced-guides/plugins/plugins", "unlisted": false}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Troubleshooting", "items": [{"type": "link", "label": "Sound/Audio", "href": "/troubleshoot/audio", "docId": "troubleshoot/audio", "unlisted": false}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Contributing", "items": [{"type": "link", "label": "Introduction", "href": "/guides/contributing/", "docId": "guides/contributing/contributing", "unlisted": false}, {"type": "link", "label": "Locales Guide", "href": "/guides/contributing/locales", "docId": "guides/contributing/locales", "unlisted": false}], "collapsed": true, "collapsible": true}], "communitySidebar": [{"type": "link", "label": "Community", "href": "/community/", "docId": "community/community", "unlisted": false}, {"type": "link", "label": "Credits", "href": "/community/credits", "docId": "community/credits", "unlisted": false}, {"type": "category", "label": "Development", "collapsible": true, "collapsed": true, "items": [{"type": "category", "label": "Architecture", "collapsible": true, "collapsed": true, "items": [{"type": "link", "label": "Client", "href": "/community/development/architecture/client/", "docId": "community/development/architecture/client/architecture-client", "unlisted": false}], "href": "/community/development/architecture/"}, {"type": "link", "label": "Gallery", "href": "/community/development/gallery/", "docId": "community/development/gallery/gallery", "unlisted": false}], "href": "/community/development/"}, {"type": "link", "label": "Discord", "href": "/community/discord", "docId": "community/discord", "unlisted": false}], "developmentSidebar": [{"type": "link", "label": "Gallery", "href": "/development/gallery/", "docId": "development/gallery/gallery", "unlisted": false}, {"type": "link", "label": "Development", "href": "/development/", "docId": "development/development", "unlisted": false}]}, "docs": {"advanced-guides/plugins/plugins": {"id": "advanced-guides/plugins/plugins", "title": "Plugins", "description": "Plugins are currently not available.", "sidebar": "docsSideBar"}, "bot/index": {"id": "bot/index", "title": "index", "description": ""}, "community/community": {"id": "community/community", "title": "Community", "description": "Links", "sidebar": "communitySidebar"}, "community/credits": {"id": "community/credits", "title": "Credits", "description": "This project is made possible by the community surrounding it and especially the wonderful people and projects listed in this document.", "sidebar": "communitySidebar"}, "community/development/architecture/client/architecture-client": {"id": "community/development/architecture/client/architecture-client", "title": "Client", "description": "---", "sidebar": "communitySidebar"}, "community/development/architecture/dev-architecture": {"id": "community/development/architecture/dev-architecture", "title": "Architecture", "description": "This part is to show some of the internal architecture of PianoRhythm's client (frontend) and server (backend).", "sidebar": "communitySidebar"}, "community/development/development": {"id": "community/development/development", "title": "Development", "description": "Welcome to the section about PianoRhythm's development!", "sidebar": "communitySidebar"}, "community/development/gallery/gallery": {"id": "community/development/gallery/gallery", "title": "Gallery", "description": "---", "sidebar": "communitySidebar"}, "community/discord": {"id": "community/discord", "title": "Discord", "description": "Join our discord server here!", "sidebar": "communitySidebar"}, "development/development": {"id": "development/development", "title": "Development", "description": "Welcome to the section about PianoRhythm's development!", "sidebar": "developmentSidebar"}, "development/gallery/gallery": {"id": "development/gallery/gallery", "title": "Gallery", "description": "---", "sidebar": "developmentSidebar"}, "faqs/faqs": {"id": "faqs/faqs", "title": "PianoRhythm: FAQs", "description": "<FAQStructuredData title=\"Common FAQs\" faqs={["}, "guides/components/looper": {"id": "guides/components/looper", "title": "<PERSON><PERSON>", "description": "Introduction", "sidebar": "docsSideBar"}, "guides/contributing/contributing": {"id": "guides/contributing/contributing", "title": "Introduction", "description": "Introduction", "sidebar": "docsSideBar"}, "guides/contributing/locales": {"id": "guides/contributing/locales", "title": "Locales Guide", "description": "Introduction", "sidebar": "docsSideBar"}, "guides/customization/customization-piano": {"id": "guides/customization/customization-piano", "title": "Piano Customization", "description": "Introduction", "sidebar": "docsSideBar"}, "guides/general/commands": {"id": "guides/general/commands", "title": "Commands", "description": "Chat Commands Documentation", "sidebar": "docsSideBar"}, "guides/guide": {"id": "guides/guide", "title": "General", "description": "These are just common guides about the different features of PianoRhythm.", "sidebar": "docsSideBar"}, "guides/instrument-dock/channel-parameters": {"id": "guides/instrument-dock/channel-parameters", "title": "Channel Parameters", "description": "This guide is still a work in progress.", "sidebar": "docsSideBar"}, "guides/instrument-dock/instrument-dock": {"id": "guides/instrument-dock/instrument-dock", "title": "Instrument Dock", "description": "This guide is still a work in progress", "sidebar": "docsSideBar"}, "guides/midi-player/midi-player": {"id": "guides/midi-player/midi-player", "title": "MIDI Player", "description": "Introduction", "sidebar": "docsSideBar"}, "guides/midi-player/vp-sequencer": {"id": "guides/midi-player/vp-sequencer", "title": "Virtual Piano Player", "description": "Introduction", "sidebar": "docsSideBar"}, "guides/orchestra-mode/orchestra-mode": {"id": "guides/orchestra-mode/orchestra-mode", "title": "Orchestra Mode", "description": "Introduction", "sidebar": "docsSideBar"}, "guides/rooms": {"id": "guides/rooms", "title": "Rooms", "description": "What are Rooms in PianoRhythm?", "sidebar": "docsSideBar"}, "guides/rooms/create-rooms": {"id": "guides/rooms/create-rooms", "title": "Creating Rooms", "description": "The New Room Modal in PianoRhythm allows you to create or update a room with various settings.", "sidebar": "docsSideBar"}, "guides/sequencer/midi-step-sequencer": {"id": "guides/sequencer/midi-step-sequencer", "title": "MIDI Step Sequencer", "description": "Introduction"}, "guides/sheet-music/sheetmusic": {"id": "guides/sheet-music/sheetmusic", "title": "Sheet Music", "description": "Introduction", "sidebar": "docsSideBar"}, "guides/subscription/pro-subscription": {"id": "guides/subscription/pro-subscription", "title": "PRO Subscription", "description": "General", "sidebar": "docsSideBar"}, "troubleshoot/audio": {"id": "troubleshoot/audio", "title": "Sound/Audio", "description": "General", "sidebar": "docsSideBar"}, "tutorials/tutorial-instruments": {"id": "tutorials/tutorial-instruments", "title": "Changing Instruments", "description": "A soundfont is typically comprised of a collection of instruments or technically called presets.", "sidebar": "docsSideBar"}, "tutorials/tutorial-self-host-rooms": {"id": "tutorials/tutorial-self-host-rooms", "title": "Self Hosted Rooms", "description": "Self hosting rooms is currently not available.", "sidebar": "docsSideBar"}, "tutorials/tutorial-soundfonts": {"id": "tutorials/tutorial-soundfonts", "title": "Loading Soundfonts", "description": "First of all, what is a soundfont? Well, according to Wikipedia:", "sidebar": "docsSideBar"}, "tutorials/tutorials": {"id": "tutorials/tutorials", "title": "Getting Started", "description": "Wondering how to do a certain task or thing in PianoRhythm?", "sidebar": "docsSideBar"}}}}