/*
 * AUTOGENERATED - DON'T EDIT
 * Your edits in this file will be overwritten in the next build!
 * Modify the docusaurus.config.js file at your site's root instead.
 */
export default {
  "title": "PianoRhythm",
  "tagline": "General documentation",
  "url": "http://localhost",
  "baseUrl": "/",
  "onBrokenLinks": "warn",
  "onBrokenMarkdownLinks": "warn",
  "favicon": "img/favicon.ico",
  "organizationName": "pianorhythm",
  "projectName": "pianorhythm",
  "future": {
    "v4": {
      "removeLegacyPostBuildHeadAttribute": false,
      "useCssCascadeLayers": false
    },
    "experimental_faster": {
      "swcJsLoader": false,
      "swcJsMinimizer": false,
      "swcHtmlMinimizer": false,
      "lightningCssMinimizer": false,
      "mdxCrossCompilerCache": false,
      "rspackBundler": false,
      "rspackPersistentCache": false,
      "ssgWorkerThreads": false
    },
    "experimental_storage": {
      "type": "localStorage",
      "namespace": false
    },
    "experimental_router": "browser"
  },
  "plugins": [
    null,
    [
      "C:\\Projects\\pianorhythm-docs\\src\\plugins\\webpack\\index.js",
      {}
    ],
    "C:\\Projects\\pianorhythm-docs\\node_modules\\.pnpm\\docusaurus-lunr-search@3.6.0_@docusaurus+core@3.8.1_@docusaurus+faster@3.8.1_@docusaurus+type_caqwrydzut53csp3wakq3mabau\\node_modules\\docusaurus-lunr-search\\src\\index.js",
    [
      "C:\\Projects\\pianorhythm-docs\\src\\plugins\\changelog\\index.js",
      {
        "id": "changelog",
        "blogTitle": "PianoRhythm changelog",
        "blogDescription": "Keep yourself up-to-date about new features in every release",
        "blogSidebarCount": 10,
        "blogSidebarTitle": "Changelog",
        "routeBasePath": "/changelog",
        "showReadingTime": false,
        "postsPerPage": 20,
        "archiveBasePath": null,
        "feedOptions": {
          "type": "all",
          "title": "PianoRhythm changelog",
          "description": "Keep yourself up-to-date about new features in every release",
          "copyright": "Copyright © 2025 PianoRhythm, LLC.",
          "language": "en"
        }
      }
    ],
    [
      "@grnet/docusaurus-terminology",
      {
        "termsDir": "./docs/terms",
        "docsDir": "./docs/",
        "glossaryFilepath": "./docs/glossary.md"
      }
    ],
    [
      "@docusaurus/plugin-client-redirects",
      {
        "redirects": [
          {
            "to": "/troubleshoot/audio",
            "from": [
              "/troubleshoot-audio"
            ]
          },
          {
            "to": "/guides/sheet-music",
            "from": [
              "/guide/sheetmusic",
              "/guides/sheetmusic"
            ]
          },
          {
            "to": "/guides/midi-player",
            "from": [
              "/guide/midiplayer",
              "/guide/midiplayer"
            ]
          },
          {
            "to": "/guides/customization/customization-piano",
            "from": [
              "/guides/customization-piano/"
            ]
          }
        ]
      }
    ]
  ],
  "i18n": {
    "defaultLocale": "en",
    "locales": [
      "en"
    ],
    "path": "i18n",
    "localeConfigs": {}
  },
  "presets": [
    [
      "classic",
      {
        "docs": {
          "routeBasePath": "/",
          "sidebarPath": "C:\\Projects\\pianorhythm-docs\\sidebars.js",
          "showLastUpdateAuthor": true,
          "showLastUpdateTime": true,
          "includeCurrentVersion": true,
          "exclude": [
            "./api/index.md"
          ]
        },
        "blog": {
          "id": "blog",
          "blogTitle": "PianoRhythm Blog",
          "blogDescription": "A blog about the PianoRhythm app and its development.",
          "showReadingTime": true,
          "onInlineTags": "warn",
          "onInlineAuthors": "warn",
          "onUntruncatedBlogPosts": "warn"
        },
        "theme": {
          "customCss": "C:\\Projects\\pianorhythm-docs\\src\\css\\custom.css"
        },
        "gtag": {
          "trackingID": "G-HPWMT1LLDW",
          "anonymizeIP": true
        },
        "sitemap": {
          "changefreq": "weekly",
          "priority": 0.5
        }
      }
    ]
  ],
  "themeConfig": {
    "docs": {
      "sidebar": {
        "hideable": true,
        "autoCollapseCategories": false
      },
      "versionPersistence": "localStorage"
    },
    "colorMode": {
      "defaultMode": "dark",
      "disableSwitch": true,
      "respectPrefersColorScheme": false
    },
    "navbar": {
      "title": "PianoRhythm",
      "hideOnScroll": true,
      "logo": {
        "alt": "PianoRhythm Logo",
        "src": "img/logo.png"
      },
      "items": [
        {
          "label": "Guides",
          "position": "left",
          "to": "guides",
          "items": [
            {
              "label": "Guides",
              "to": "guides"
            },
            {
              "label": "Tutorials",
              "to": "tutorials"
            },
            {
              "label": "Plugins",
              "to": "advanced-guides/plugins"
            },
            {
              "label": "FAQs",
              "to": "faqs"
            },
            {
              "label": "Troubleshoot",
              "to": "troubleshoot/audio"
            },
            {
              "label": "Contributing",
              "to": "guides/contributing"
            }
          ]
        },
        {
          "to": "blog",
          "label": "Blog",
          "position": "left"
        },
        {
          "label": "Changelog",
          "position": "left",
          "to": "/changelog"
        },
        {
          "label": "Community",
          "position": "left",
          "to": "community"
        },
        {
          "href": "http://localhost",
          "label": "Enter PianoRhythm",
          "position": "right"
        },
        {
          "href": "https://staging.pianorhythm.io",
          "label": "Enter Staging",
          "position": "right"
        }
      ]
    },
    "footer": {
      "style": "dark",
      "links": [
        {
          "title": "Docs",
          "items": [
            {
              "label": "Guides",
              "to": "guides"
            }
          ]
        },
        {
          "title": "Community",
          "items": [
            {
              "label": "Discord",
              "href": "https://discord.gg/Pm2xXxb"
            }
          ]
        },
        {
          "title": "More",
          "items": [
            {
              "label": "PianoRhythm",
              "href": "http://localhost"
            },
            {
              "label": "Status Page",
              "href": "https://pianorhythm.statuspage.io"
            }
          ]
        }
      ],
      "copyright": "Copyright © 2025 <b>PianoRhythm, LLC.</b> Built with Docusaurus."
    },
    "metadata": [
      {
        "name": "keywords",
        "content": "HTML5, WEBGL, Piano, Rust, Tauri, Documentation, PianoRhythm, PR, Multiplayer, Game, Music, Synthesizer, Instruments"
      },
      {
        "name": "description",
        "content": "General documentation for PianoRhythm"
      },
      {
        "name": "author",
        "content": "PianoRhythm, LLC"
      },
      {
        "name": "title",
        "content": "PianoRhythm Documentation"
      },
      {
        "name": "og:image",
        "content": "https://assets.pianorhythm.io/images/logo.png"
      }
    ],
    "blog": {
      "sidebar": {
        "groupByYear": true
      }
    },
    "prism": {
      "additionalLanguages": [],
      "theme": {
        "plain": {
          "color": "#bfc7d5",
          "backgroundColor": "#292d3e"
        },
        "styles": [
          {
            "types": [
              "comment"
            ],
            "style": {
              "color": "rgb(105, 112, 152)",
              "fontStyle": "italic"
            }
          },
          {
            "types": [
              "string",
              "inserted"
            ],
            "style": {
              "color": "rgb(195, 232, 141)"
            }
          },
          {
            "types": [
              "number"
            ],
            "style": {
              "color": "rgb(247, 140, 108)"
            }
          },
          {
            "types": [
              "builtin",
              "char",
              "constant",
              "function"
            ],
            "style": {
              "color": "rgb(130, 170, 255)"
            }
          },
          {
            "types": [
              "punctuation",
              "selector"
            ],
            "style": {
              "color": "rgb(199, 146, 234)"
            }
          },
          {
            "types": [
              "variable"
            ],
            "style": {
              "color": "rgb(191, 199, 213)"
            }
          },
          {
            "types": [
              "class-name",
              "attr-name"
            ],
            "style": {
              "color": "rgb(255, 203, 107)"
            }
          },
          {
            "types": [
              "tag",
              "deleted"
            ],
            "style": {
              "color": "rgb(255, 85, 114)"
            }
          },
          {
            "types": [
              "operator"
            ],
            "style": {
              "color": "rgb(137, 221, 255)"
            }
          },
          {
            "types": [
              "boolean"
            ],
            "style": {
              "color": "rgb(255, 88, 116)"
            }
          },
          {
            "types": [
              "keyword"
            ],
            "style": {
              "fontStyle": "italic"
            }
          },
          {
            "types": [
              "doctype"
            ],
            "style": {
              "color": "rgb(199, 146, 234)",
              "fontStyle": "italic"
            }
          },
          {
            "types": [
              "namespace"
            ],
            "style": {
              "color": "rgb(178, 204, 214)"
            }
          },
          {
            "types": [
              "url"
            ],
            "style": {
              "color": "rgb(221, 221, 221)"
            }
          }
        ]
      },
      "magicComments": [
        {
          "className": "theme-code-block-highlighted-line",
          "line": "highlight-next-line",
          "block": {
            "start": "highlight-start",
            "end": "highlight-end"
          }
        }
      ]
    },
    "tableOfContents": {
      "minHeadingLevel": 2,
      "maxHeadingLevel": 3
    },
    "mermaid": {
      "theme": {
        "dark": "dark",
        "light": "default"
      },
      "options": {}
    }
  },
  "scripts": [
    "/js/analytics.js",
    "/js/pianorhythm-changelog-check.js"
  ],
  "headTags": [
    {
      "tagName": "meta",
      "attributes": {
        "property": "og:image",
        "content": "https://assets.pianorhythm.io/images/logo.png"
      }
    },
    {
      "tagName": "meta",
      "attributes": {
        "property": "og:type",
        "content": "website"
      }
    }
  ],
  "markdown": {
    "format": "mdx",
    "mermaid": true,
    "mdx1Compat": {
      "comments": true,
      "admonitions": true,
      "headingIds": true
    },
    "anchors": {
      "maintainCase": false
    }
  },
  "stylesheets": [],
  "themes": [
    "@docusaurus/theme-mermaid"
  ],
  "baseUrlIssueBanner": true,
  "onBrokenAnchors": "warn",
  "onDuplicateRoutes": "warn",
  "staticDirectories": [
    "static"
  ],
  "customFields": {},
  "clientModules": [],
  "titleDelimiter": "|",
  "noIndex": false
};
