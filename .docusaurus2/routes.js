import React from 'react';
import ComponentCreator from '@docusaurus/ComponentCreator';

export default [
  {
    path: '/blog',
    component: ComponentCreator('/blog', '671'),
    exact: true
  },
  {
    path: '/blog/archive',
    component: ComponentCreator('/blog/archive', 'd1d'),
    exact: true
  },
  {
    path: '/blog/authors',
    component: ComponentCreator('/blog/authors', '463'),
    exact: true
  },
  {
    path: '/blog/back-from-hiatus',
    component: ComponentCreator('/blog/back-from-hiatus', '81f'),
    exact: true
  },
  {
    path: '/blog/tags',
    component: ComponentCreator('/blog/tags', 'efb'),
    exact: true
  },
  {
    path: '/blog/tags/comeback',
    component: ComponentCreator('/blog/tags/comeback', 'ca6'),
    exact: true
  },
  {
    path: '/blog/tags/hiatus',
    component: ComponentCreator('/blog/tags/hiatus', '9a6'),
    exact: true
  },
  {
    path: '/blog/tags/history',
    component: ComponentCreator('/blog/tags/history', 'e86'),
    exact: true
  },
  {
    path: '/blog/tags/oak',
    component: ComponentCreator('/blog/tags/oak', 'ba2'),
    exact: true
  },
  {
    path: '/blog/tags/pianorhythm',
    component: ComponentCreator('/blog/tags/pianorhythm', '0d7'),
    exact: true
  },
  {
    path: '/blog/tags/server-upgrade',
    component: ComponentCreator('/blog/tags/server-upgrade', 'a66'),
    exact: true
  },
  {
    path: '/blog/tags/staging',
    component: ComponentCreator('/blog/tags/staging', '61b'),
    exact: true
  },
  {
    path: '/blog/tags/v-0-10-0',
    component: ComponentCreator('/blog/tags/v-0-10-0', '874'),
    exact: true
  },
  {
    path: '/blog/tags/v-2',
    component: ComponentCreator('/blog/tags/v-2', 'aff'),
    exact: true
  },
  {
    path: '/blog/tags/v-2-history',
    component: ComponentCreator('/blog/tags/v-2-history', 'b46'),
    exact: true
  },
  {
    path: '/blog/tags/v-3',
    component: ComponentCreator('/blog/tags/v-3', '3d4'),
    exact: true
  },
  {
    path: '/blog/tags/welcome',
    component: ComponentCreator('/blog/tags/welcome', 'c3a'),
    exact: true
  },
  {
    path: '/blog/update_2023',
    component: ComponentCreator('/blog/update_2023', '415'),
    exact: true
  },
  {
    path: '/blog/welcome',
    component: ComponentCreator('/blog/welcome', 'a18'),
    exact: true
  },
  {
    path: '/changelog',
    component: ComponentCreator('/changelog', '987'),
    exact: true
  },
  {
    path: '/changelog/2022/12/07/0.7.91',
    component: ComponentCreator('/changelog/2022/12/07/0.7.91', 'ac3'),
    exact: true
  },
  {
    path: '/changelog/2022/12/18/0.7.119',
    component: ComponentCreator('/changelog/2022/12/18/0.7.119', '103'),
    exact: true
  },
  {
    path: '/changelog/2022/12/20/0.7.123',
    component: ComponentCreator('/changelog/2022/12/20/0.7.123', 'fdd'),
    exact: true
  },
  {
    path: '/changelog/2022/12/26/0.7.143',
    component: ComponentCreator('/changelog/2022/12/26/0.7.143', '2f4'),
    exact: true
  },
  {
    path: '/changelog/2022/12/26/0.7.146',
    component: ComponentCreator('/changelog/2022/12/26/0.7.146', '8bc'),
    exact: true
  },
  {
    path: '/changelog/2023/01/09/0.7.152',
    component: ComponentCreator('/changelog/2023/01/09/0.7.152', 'e33'),
    exact: true
  },
  {
    path: '/changelog/2023/03/13/0.7.222',
    component: ComponentCreator('/changelog/2023/03/13/0.7.222', 'd69'),
    exact: true
  },
  {
    path: '/changelog/2023/03/18/0.7.230',
    component: ComponentCreator('/changelog/2023/03/18/0.7.230', '3bc'),
    exact: true
  },
  {
    path: '/changelog/2023/05/04/0.8.0',
    component: ComponentCreator('/changelog/2023/05/04/0.8.0', '4b2'),
    exact: true
  },
  {
    path: '/changelog/2023/05/27/0.8.24',
    component: ComponentCreator('/changelog/2023/05/27/0.8.24', 'a25'),
    exact: true
  },
  {
    path: '/changelog/2023/06/17/0.8.26',
    component: ComponentCreator('/changelog/2023/06/17/0.8.26', 'fd4'),
    exact: true
  },
  {
    path: '/changelog/2023/07/17/0.8.30',
    component: ComponentCreator('/changelog/2023/07/17/0.8.30', 'cd7'),
    exact: true
  },
  {
    path: '/changelog/2023/09/05/0.8.39',
    component: ComponentCreator('/changelog/2023/09/05/0.8.39', '3fa'),
    exact: true
  },
  {
    path: '/changelog/2023/09/09/0.8.40',
    component: ComponentCreator('/changelog/2023/09/09/0.8.40', 'c86'),
    exact: true
  },
  {
    path: '/changelog/2023/09/10/0.8.41',
    component: ComponentCreator('/changelog/2023/09/10/0.8.41', 'fee'),
    exact: true
  },
  {
    path: '/changelog/2023/09/11/0.8.42',
    component: ComponentCreator('/changelog/2023/09/11/0.8.42', 'e32'),
    exact: true
  },
  {
    path: '/changelog/2023/09/12/0.8.43',
    component: ComponentCreator('/changelog/2023/09/12/0.8.43', '57f'),
    exact: true
  },
  {
    path: '/changelog/2023/09/20/0.8.44',
    component: ComponentCreator('/changelog/2023/09/20/0.8.44', '533'),
    exact: true
  },
  {
    path: '/changelog/2023/09/23/0.8.45',
    component: ComponentCreator('/changelog/2023/09/23/0.8.45', '9ce'),
    exact: true
  },
  {
    path: '/changelog/2023/09/29/0.8.46',
    component: ComponentCreator('/changelog/2023/09/29/0.8.46', 'e4a'),
    exact: true
  },
  {
    path: '/changelog/2023/12/31/0.9.0',
    component: ComponentCreator('/changelog/2023/12/31/0.9.0', '2af'),
    exact: true
  },
  {
    path: '/changelog/2024/05/07/0.9.9',
    component: ComponentCreator('/changelog/2024/05/07/0.9.9', 'c19'),
    exact: true
  },
  {
    path: '/changelog/page/2',
    component: ComponentCreator('/changelog/page/2', '37a'),
    exact: true
  },
  {
    path: '/changelog/tags',
    component: ComponentCreator('/changelog/tags', 'a5b'),
    exact: true
  },
  {
    path: '/changelog/tags/0-7-119',
    component: ComponentCreator('/changelog/tags/0-7-119', 'ab5'),
    exact: true
  },
  {
    path: '/changelog/tags/0-7-123',
    component: ComponentCreator('/changelog/tags/0-7-123', 'fce'),
    exact: true
  },
  {
    path: '/changelog/tags/0-7-143',
    component: ComponentCreator('/changelog/tags/0-7-143', '9b6'),
    exact: true
  },
  {
    path: '/changelog/tags/0-7-146',
    component: ComponentCreator('/changelog/tags/0-7-146', 'd65'),
    exact: true
  },
  {
    path: '/changelog/tags/0-7-152',
    component: ComponentCreator('/changelog/tags/0-7-152', '259'),
    exact: true
  },
  {
    path: '/changelog/tags/0-7-222',
    component: ComponentCreator('/changelog/tags/0-7-222', '3bf'),
    exact: true
  },
  {
    path: '/changelog/tags/0-7-230',
    component: ComponentCreator('/changelog/tags/0-7-230', '5ee'),
    exact: true
  },
  {
    path: '/changelog/tags/0-7-91',
    component: ComponentCreator('/changelog/tags/0-7-91', '53b'),
    exact: true
  },
  {
    path: '/changelog/tags/0-8-0',
    component: ComponentCreator('/changelog/tags/0-8-0', 'ecb'),
    exact: true
  },
  {
    path: '/changelog/tags/0-8-24',
    component: ComponentCreator('/changelog/tags/0-8-24', '233'),
    exact: true
  },
  {
    path: '/changelog/tags/0-8-26',
    component: ComponentCreator('/changelog/tags/0-8-26', '17c'),
    exact: true
  },
  {
    path: '/changelog/tags/0-8-30',
    component: ComponentCreator('/changelog/tags/0-8-30', '4f5'),
    exact: true
  },
  {
    path: '/changelog/tags/0-8-39',
    component: ComponentCreator('/changelog/tags/0-8-39', '3d2'),
    exact: true
  },
  {
    path: '/changelog/tags/0-8-40',
    component: ComponentCreator('/changelog/tags/0-8-40', 'ba1'),
    exact: true
  },
  {
    path: '/changelog/tags/0-8-41',
    component: ComponentCreator('/changelog/tags/0-8-41', '2c6'),
    exact: true
  },
  {
    path: '/changelog/tags/0-8-42',
    component: ComponentCreator('/changelog/tags/0-8-42', '2f3'),
    exact: true
  },
  {
    path: '/changelog/tags/0-8-43',
    component: ComponentCreator('/changelog/tags/0-8-43', '4bc'),
    exact: true
  },
  {
    path: '/changelog/tags/0-8-44',
    component: ComponentCreator('/changelog/tags/0-8-44', '5f1'),
    exact: true
  },
  {
    path: '/changelog/tags/0-8-45',
    component: ComponentCreator('/changelog/tags/0-8-45', 'c47'),
    exact: true
  },
  {
    path: '/changelog/tags/0-8-46',
    component: ComponentCreator('/changelog/tags/0-8-46', '227'),
    exact: true
  },
  {
    path: '/changelog/tags/0-9-0',
    component: ComponentCreator('/changelog/tags/0-9-0', '07c'),
    exact: true
  },
  {
    path: '/changelog/tags/0-9-9',
    component: ComponentCreator('/changelog/tags/0-9-9', '92d'),
    exact: true
  },
  {
    path: '/changelog/tags/changelog',
    component: ComponentCreator('/changelog/tags/changelog', '6d2'),
    exact: true
  },
  {
    path: '/changelog/tags/changelog/page/2',
    component: ComponentCreator('/changelog/tags/changelog/page/2', 'e51'),
    exact: true
  },
  {
    path: '/',
    component: ComponentCreator('/', '2e1'),
    exact: true
  },
  {
    path: '/',
    component: ComponentCreator('/', '6c4'),
    routes: [
      {
        path: '/',
        component: ComponentCreator('/', 'e83'),
        routes: [
          {
            path: '/tags',
            component: ComponentCreator('/tags', 'ce1'),
            exact: true
          },
          {
            path: '/tags/architecture',
            component: ComponentCreator('/tags/architecture', '851'),
            exact: true
          },
          {
            path: '/tags/audio',
            component: ComponentCreator('/tags/audio', '2c0'),
            exact: true
          },
          {
            path: '/tags/channel-parameters',
            component: ComponentCreator('/tags/channel-parameters', 'e20'),
            exact: true
          },
          {
            path: '/tags/client',
            component: ComponentCreator('/tags/client', 'a0b'),
            exact: true
          },
          {
            path: '/tags/contributing',
            component: ComponentCreator('/tags/contributing', '0b1'),
            exact: true
          },
          {
            path: '/tags/create-room',
            component: ComponentCreator('/tags/create-room', '6cd'),
            exact: true
          },
          {
            path: '/tags/customization',
            component: ComponentCreator('/tags/customization', '084'),
            exact: true
          },
          {
            path: '/tags/desktop',
            component: ComponentCreator('/tags/desktop', '4b0'),
            exact: true
          },
          {
            path: '/tags/discord',
            component: ComponentCreator('/tags/discord', 'ff6'),
            exact: true
          },
          {
            path: '/tags/faqs',
            component: ComponentCreator('/tags/faqs', 'eef'),
            exact: true
          },
          {
            path: '/tags/frontend',
            component: ComponentCreator('/tags/frontend', '1d6'),
            exact: true
          },
          {
            path: '/tags/guide',
            component: ComponentCreator('/tags/guide', '702'),
            exact: true
          },
          {
            path: '/tags/host',
            component: ComponentCreator('/tags/host', '030'),
            exact: true
          },
          {
            path: '/tags/instrument-dock',
            component: ComponentCreator('/tags/instrument-dock', '1f9'),
            exact: true
          },
          {
            path: '/tags/instruments',
            component: ComponentCreator('/tags/instruments', 'd28'),
            exact: true
          },
          {
            path: '/tags/java-script',
            component: ComponentCreator('/tags/java-script', '873'),
            exact: true
          },
          {
            path: '/tags/locales',
            component: ComponentCreator('/tags/locales', '603'),
            exact: true
          },
          {
            path: '/tags/looper',
            component: ComponentCreator('/tags/looper', 'd17'),
            exact: true
          },
          {
            path: '/tags/midi',
            component: ComponentCreator('/tags/midi', '6cb'),
            exact: true
          },
          {
            path: '/tags/new-room',
            component: ComponentCreator('/tags/new-room', '6c9'),
            exact: true
          },
          {
            path: '/tags/orchestra',
            component: ComponentCreator('/tags/orchestra', '183'),
            exact: true
          },
          {
            path: '/tags/piano',
            component: ComponentCreator('/tags/piano', '3e9'),
            exact: true
          },
          {
            path: '/tags/piano-customization',
            component: ComponentCreator('/tags/piano-customization', 'd38'),
            exact: true
          },
          {
            path: '/tags/plugins',
            component: ComponentCreator('/tags/plugins', '001'),
            exact: true
          },
          {
            path: '/tags/pro',
            component: ComponentCreator('/tags/pro', '221'),
            exact: true
          },
          {
            path: '/tags/pro-subscription',
            component: ComponentCreator('/tags/pro-subscription', 'fd3'),
            exact: true
          },
          {
            path: '/tags/questions',
            component: ComponentCreator('/tags/questions', '62a'),
            exact: true
          },
          {
            path: '/tags/rooms',
            component: ComponentCreator('/tags/rooms', 'b47'),
            exact: true
          },
          {
            path: '/tags/rust',
            component: ComponentCreator('/tags/rust', 'bd7'),
            exact: true
          },
          {
            path: '/tags/self-host',
            component: ComponentCreator('/tags/self-host', '43b'),
            exact: true
          },
          {
            path: '/tags/self-host-rooms',
            component: ComponentCreator('/tags/self-host-rooms', 'ef6'),
            exact: true
          },
          {
            path: '/tags/sequencer',
            component: ComponentCreator('/tags/sequencer', '4a5'),
            exact: true
          },
          {
            path: '/tags/sheet-music',
            component: ComponentCreator('/tags/sheet-music', 'aa4'),
            exact: true
          },
          {
            path: '/tags/social-media',
            component: ComponentCreator('/tags/social-media', '47f'),
            exact: true
          },
          {
            path: '/tags/sound',
            component: ComponentCreator('/tags/sound', '909'),
            exact: true
          },
          {
            path: '/tags/soundfonts',
            component: ComponentCreator('/tags/soundfonts', '7f8'),
            exact: true
          },
          {
            path: '/tags/step-sequencer',
            component: ComponentCreator('/tags/step-sequencer', '1b0'),
            exact: true
          },
          {
            path: '/tags/troubleshoot',
            component: ComponentCreator('/tags/troubleshoot', '062'),
            exact: true
          },
          {
            path: '/tags/tutorial',
            component: ComponentCreator('/tags/tutorial', 'bc6'),
            exact: true
          },
          {
            path: '/tags/twitch',
            component: ComponentCreator('/tags/twitch', '619'),
            exact: true
          },
          {
            path: '/tags/update-room',
            component: ComponentCreator('/tags/update-room', '90b'),
            exact: true
          },
          {
            path: '/tags/web-development',
            component: ComponentCreator('/tags/web-development', 'ced'),
            exact: true
          },
          {
            path: '/tags/x',
            component: ComponentCreator('/tags/x', 'ddc'),
            exact: true
          },
          {
            path: '/tags/youtube',
            component: ComponentCreator('/tags/youtube', 'fbc'),
            exact: true
          },
          {
            path: '/',
            component: ComponentCreator('/', 'd2f'),
            routes: [
              {
                path: '/advanced-guides/plugins/',
                component: ComponentCreator('/advanced-guides/plugins/', '4bf'),
                exact: true,
                sidebar: "docsSideBar"
              },
              {
                path: '/bot/',
                component: ComponentCreator('/bot/', '62c'),
                exact: true
              },
              {
                path: '/community/',
                component: ComponentCreator('/community/', 'a7e'),
                exact: true,
                sidebar: "communitySidebar"
              },
              {
                path: '/community/credits',
                component: ComponentCreator('/community/credits', 'd15'),
                exact: true,
                sidebar: "communitySidebar"
              },
              {
                path: '/community/development/',
                component: ComponentCreator('/community/development/', '4bf'),
                exact: true,
                sidebar: "communitySidebar"
              },
              {
                path: '/community/development/architecture/',
                component: ComponentCreator('/community/development/architecture/', '560'),
                exact: true,
                sidebar: "communitySidebar"
              },
              {
                path: '/community/development/architecture/client/',
                component: ComponentCreator('/community/development/architecture/client/', '87f'),
                exact: true,
                sidebar: "communitySidebar"
              },
              {
                path: '/community/development/gallery/',
                component: ComponentCreator('/community/development/gallery/', 'cd9'),
                exact: true,
                sidebar: "communitySidebar"
              },
              {
                path: '/community/discord',
                component: ComponentCreator('/community/discord', '092'),
                exact: true,
                sidebar: "communitySidebar"
              },
              {
                path: '/development/',
                component: ComponentCreator('/development/', 'e24'),
                exact: true,
                sidebar: "developmentSidebar"
              },
              {
                path: '/development/gallery/',
                component: ComponentCreator('/development/gallery/', '211'),
                exact: true,
                sidebar: "developmentSidebar"
              },
              {
                path: '/faqs/',
                component: ComponentCreator('/faqs/', 'ed4'),
                exact: true
              },
              {
                path: '/guides/',
                component: ComponentCreator('/guides/', '658'),
                exact: true,
                sidebar: "docsSideBar"
              },
              {
                path: '/guides/components/looper',
                component: ComponentCreator('/guides/components/looper', '825'),
                exact: true,
                sidebar: "docsSideBar"
              },
              {
                path: '/guides/contributing/',
                component: ComponentCreator('/guides/contributing/', '5b1'),
                exact: true,
                sidebar: "docsSideBar"
              },
              {
                path: '/guides/contributing/locales',
                component: ComponentCreator('/guides/contributing/locales', '25f'),
                exact: true,
                sidebar: "docsSideBar"
              },
              {
                path: '/guides/customization/customization-piano',
                component: ComponentCreator('/guides/customization/customization-piano', '02f'),
                exact: true,
                sidebar: "docsSideBar"
              },
              {
                path: '/guides/general/commands',
                component: ComponentCreator('/guides/general/commands', '6a0'),
                exact: true,
                sidebar: "docsSideBar"
              },
              {
                path: '/guides/instrument-dock/',
                component: ComponentCreator('/guides/instrument-dock/', 'c33'),
                exact: true,
                sidebar: "docsSideBar"
              },
              {
                path: '/guides/instrument-dock/channel-parameters',
                component: ComponentCreator('/guides/instrument-dock/channel-parameters', '6d8'),
                exact: true,
                sidebar: "docsSideBar"
              },
              {
                path: '/guides/midi-player/',
                component: ComponentCreator('/guides/midi-player/', '348'),
                exact: true,
                sidebar: "docsSideBar"
              },
              {
                path: '/guides/midi-player/vp-sequencer',
                component: ComponentCreator('/guides/midi-player/vp-sequencer', '29b'),
                exact: true,
                sidebar: "docsSideBar"
              },
              {
                path: '/guides/orchestra-mode/',
                component: ComponentCreator('/guides/orchestra-mode/', '16c'),
                exact: true,
                sidebar: "docsSideBar"
              },
              {
                path: '/guides/rooms',
                component: ComponentCreator('/guides/rooms', '1b0'),
                exact: true,
                sidebar: "docsSideBar"
              },
              {
                path: '/guides/rooms/create-rooms',
                component: ComponentCreator('/guides/rooms/create-rooms', '0d6'),
                exact: true,
                sidebar: "docsSideBar"
              },
              {
                path: '/guides/sequencer/',
                component: ComponentCreator('/guides/sequencer/', 'ea0'),
                exact: true
              },
              {
                path: '/guides/sheet-music/',
                component: ComponentCreator('/guides/sheet-music/', 'd64'),
                exact: true,
                sidebar: "docsSideBar"
              },
              {
                path: '/guides/subscription/',
                component: ComponentCreator('/guides/subscription/', '948'),
                exact: true,
                sidebar: "docsSideBar"
              },
              {
                path: '/troubleshoot/audio',
                component: ComponentCreator('/troubleshoot/audio', '23a'),
                exact: true,
                sidebar: "docsSideBar"
              },
              {
                path: '/tutorials/',
                component: ComponentCreator('/tutorials/', '538'),
                exact: true,
                sidebar: "docsSideBar"
              },
              {
                path: '/tutorials/tutorial-instruments',
                component: ComponentCreator('/tutorials/tutorial-instruments', '03f'),
                exact: true,
                sidebar: "docsSideBar"
              },
              {
                path: '/tutorials/tutorial-self-host-rooms',
                component: ComponentCreator('/tutorials/tutorial-self-host-rooms', 'cff'),
                exact: true,
                sidebar: "docsSideBar"
              },
              {
                path: '/tutorials/tutorial-soundfonts',
                component: ComponentCreator('/tutorials/tutorial-soundfonts', '86f'),
                exact: true,
                sidebar: "docsSideBar"
              }
            ]
          }
        ]
      }
    ]
  },
  {
    path: '*',
    component: ComponentCreator('*'),
  },
];
