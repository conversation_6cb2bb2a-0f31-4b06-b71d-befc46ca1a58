{"allContent": {"docusaurus-plugin-content-docs": {"default": {"loadedVersions": [{"versionName": "current", "label": "Next", "banner": null, "badge": false, "noIndex": false, "className": "docs-version-current", "path": "/", "tagsPath": "/tags", "isLast": true, "routePriority": -1, "sidebarFilePath": "C:\\Projects\\pianorhythm-docs\\sidebars.js", "contentPath": "C:\\Projects\\pianorhythm-docs\\docs", "contentPathLocalized": "C:\\Projects\\pianorhythm-docs\\i18n\\en\\docusaurus-plugin-content-docs\\current", "docs": [{"id": "advanced-guides/plugins/plugins", "title": "Plugins", "description": "Plugins are currently not available.", "source": "@site/docs/advanced-guides/plugins/index.mdx", "sourceDirName": "advanced-guides/plugins", "slug": "/advanced-guides/plugins/", "permalink": "/advanced-guides/plugins/", "draft": false, "unlisted": false, "tags": [{"inline": true, "label": "plugins", "permalink": "/tags/plugins"}, {"inline": true, "label": "tutorial", "permalink": "/tags/tutorial"}], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055000, "frontMatter": {"title": "Plugins", "id": "plugins", "tags": ["plugins", "tutorial"]}, "sidebar": "docsSideBar", "previous": {"title": "Self Hosted Rooms", "permalink": "/tutorials/tutorial-self-host-rooms"}, "next": {"title": "Sound/Audio", "permalink": "/troubleshoot/audio"}}, {"id": "bot/index", "title": "index", "description": "", "source": "@site/docs/bot/index.mdx", "sourceDirName": "bot", "slug": "/bot/", "permalink": "/bot/", "draft": false, "unlisted": false, "tags": [], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055000, "frontMatter": {}}, {"id": "community/community", "title": "Community", "description": "Links", "source": "@site/docs/community/index.mdx", "sourceDirName": "community", "slug": "/community/", "permalink": "/community/", "draft": false, "unlisted": false, "tags": [{"inline": true, "label": "social media", "permalink": "/tags/social-media"}, {"inline": true, "label": "youtube", "permalink": "/tags/youtube"}, {"inline": true, "label": "twitch", "permalink": "/tags/twitch"}, {"inline": true, "label": "discord", "permalink": "/tags/discord"}, {"inline": true, "label": "x", "permalink": "/tags/x"}], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055000, "sidebarPosition": 1, "frontMatter": {"id": "community", "title": "Community", "sidebar_position": 1, "displayed_sidebar": "communitySidebar", "keywords": ["official email", "support email", "issue tracker", "social media", "youtube", "twitch", "discord"], "path": ["/community"], "tags": ["social media", "youtube", "twitch", "discord", "x"]}, "sidebar": "communitySidebar", "next": {"title": "Credits", "permalink": "/community/credits"}}, {"id": "community/credits", "title": "Credits", "description": "This project is made possible by the community surrounding it and especially the wonderful people and projects listed in this document.", "source": "@site/docs/community/credits.mdx", "sourceDirName": "community", "slug": "/community/credits", "permalink": "/community/credits", "draft": false, "unlisted": false, "tags": [], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055000, "frontMatter": {"id": "credits", "title": "Credits", "keywords": ["credits", "contributors", "libraries"], "path": ["/community/credits"]}, "sidebar": "communitySidebar", "previous": {"title": "Community", "permalink": "/community/"}, "next": {"title": "Development", "permalink": "/community/development/"}}, {"id": "community/development/architecture/client/architecture-client", "title": "Client", "description": "---", "source": "@site/docs/community/development/architecture/client/index.md", "sourceDirName": "community/development/architecture/client", "slug": "/community/development/architecture/client/", "permalink": "/community/development/architecture/client/", "draft": false, "unlisted": false, "tags": [{"inline": true, "label": "architecture", "permalink": "/tags/architecture"}, {"inline": true, "label": "client", "permalink": "/tags/client"}, {"inline": true, "label": "frontend", "permalink": "/tags/frontend"}, {"inline": true, "label": "desktop", "permalink": "/tags/desktop"}, {"inline": true, "label": "web development", "permalink": "/tags/web-development"}, {"inline": true, "label": "JavaScript", "permalink": "/tags/java-script"}, {"inline": true, "label": "Rust", "permalink": "/tags/rust"}], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055000, "frontMatter": {"title": "Client", "id": "architecture-client", "path": ["/development/architecture/client"], "tags": ["architecture", "client", "frontend", "desktop", "web development", "JavaScript", "Rust"]}, "sidebar": "communitySidebar", "previous": {"title": "Architecture", "permalink": "/community/development/architecture/"}, "next": {"title": "Gallery", "permalink": "/community/development/gallery/"}}, {"id": "community/development/architecture/dev-architecture", "title": "Architecture", "description": "This part is to show some of the internal architecture of PianoRhythm's client (frontend) and server (backend).", "source": "@site/docs/community/development/architecture/index.md", "sourceDirName": "community/development/architecture", "slug": "/community/development/architecture/", "permalink": "/community/development/architecture/", "draft": false, "unlisted": false, "tags": [{"inline": true, "label": "architecture", "permalink": "/tags/architecture"}], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055000, "frontMatter": {"title": "Architecture", "id": "dev-architecture", "path": ["/community/development/architecture/"], "tags": ["architecture"]}, "sidebar": "communitySidebar", "previous": {"title": "Development", "permalink": "/community/development/"}, "next": {"title": "Client", "permalink": "/community/development/architecture/client/"}}, {"id": "community/development/development", "title": "Development", "description": "Welcome to the section about PianoRhythm's development!", "source": "@site/docs/community/development/index.md", "sourceDirName": "community/development", "slug": "/community/development/", "permalink": "/community/development/", "draft": false, "unlisted": false, "tags": [], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055000, "frontMatter": {"id": "development", "title": "Development", "path": ["/development"]}, "sidebar": "communitySidebar", "previous": {"title": "Credits", "permalink": "/community/credits"}, "next": {"title": "Architecture", "permalink": "/community/development/architecture/"}}, {"id": "community/development/gallery/gallery", "title": "Gallery", "description": "---", "source": "@site/docs/community/development/gallery/index.mdx", "sourceDirName": "community/development/gallery", "slug": "/community/development/gallery/", "permalink": "/community/development/gallery/", "draft": false, "unlisted": false, "tags": [], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055000, "frontMatter": {"id": "gallery", "title": "Gallery", "path": ["/development/gallery"]}, "sidebar": "communitySidebar", "previous": {"title": "Client", "permalink": "/community/development/architecture/client/"}, "next": {"title": "Discord", "permalink": "/community/discord"}}, {"id": "community/discord", "title": "Discord", "description": "Join our discord server here!", "source": "@site/docs/community/discord.mdx", "sourceDirName": "community", "slug": "/community/discord", "permalink": "/community/discord", "draft": false, "unlisted": false, "tags": [], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055000, "frontMatter": {"id": "discord", "title": "Discord", "keywords": ["discord", "social media"], "path": ["/community/discord"]}, "sidebar": "communitySidebar", "previous": {"title": "Gallery", "permalink": "/community/development/gallery/"}}, {"id": "development/development", "title": "Development", "description": "Welcome to the section about PianoRhythm's development!", "source": "@site/docs/development/index.md", "sourceDirName": "development", "slug": "/development/", "permalink": "/development/", "draft": false, "unlisted": false, "tags": [], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055000, "sidebarPosition": 1, "frontMatter": {"id": "development", "title": "Development", "sidebar_position": 1, "displayed_sidebar": "developmentSidebar", "path": ["/development"]}, "sidebar": "developmentSidebar", "previous": {"title": "Gallery", "permalink": "/development/gallery/"}}, {"id": "development/gallery/gallery", "title": "Gallery", "description": "---", "source": "@site/docs/development/gallery/index.mdx", "sourceDirName": "development/gallery", "slug": "/development/gallery/", "permalink": "/development/gallery/", "draft": false, "unlisted": false, "tags": [], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055000, "sidebarPosition": 1, "frontMatter": {"id": "gallery", "title": "Gallery", "sidebar_position": 1, "displayed_sidebar": "developmentSidebar", "path": ["/development/gallery"]}, "sidebar": "developmentSidebar", "next": {"title": "Development", "permalink": "/development/"}}, {"id": "faqs/faqs", "title": "PianoRhythm: FAQs", "description": "<FAQStructuredData title=\"Common FAQs\" faqs={[", "source": "@site/docs/faqs/index.mdx", "sourceDirName": "faqs", "slug": "/faqs/", "permalink": "/faqs/", "draft": false, "unlisted": false, "tags": [{"inline": true, "label": "faqs", "permalink": "/tags/faqs"}, {"inline": true, "label": "questions", "permalink": "/tags/questions"}, {"inline": true, "label": "troubleshoot", "permalink": "/tags/troubleshoot"}], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055000, "frontMatter": {"id": "faqs", "title": "PianoRhythm: FAQs", "authors": "oak", "tags": ["faqs", "questions", "troubleshoot"], "keywords": ["faqs", "questions", "troubleshoot"], "path": ["/faqs"]}}, {"id": "guides/components/looper", "title": "<PERSON><PERSON>", "description": "Introduction", "source": "@site/docs/guides/components/looper.mdx", "sourceDirName": "guides/components", "slug": "/guides/components/looper", "permalink": "/guides/components/looper", "draft": false, "unlisted": false, "tags": [{"inline": true, "label": "looper", "permalink": "/tags/looper"}, {"inline": true, "label": "guide", "permalink": "/tags/guide"}], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055000, "frontMatter": {"title": "<PERSON><PERSON>", "id": "looper", "keywords": ["looper", "guide"], "path": ["/guides/components/looper"], "tags": ["looper", "guide"]}, "sidebar": "docsSideBar", "previous": {"title": "Virtual Piano Player", "permalink": "/guides/midi-player/vp-sequencer"}, "next": {"title": "Orchestra Mode", "permalink": "/guides/orchestra-mode/"}}, {"id": "guides/contributing/contributing", "title": "Introduction", "description": "Introduction", "source": "@site/docs/guides/contributing/index.mdx", "sourceDirName": "guides/contributing", "slug": "/guides/contributing/", "permalink": "/guides/contributing/", "draft": false, "unlisted": false, "tags": [{"inline": true, "label": "contributing", "permalink": "/tags/contributing"}, {"inline": true, "label": "guide", "permalink": "/tags/guide"}], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055000, "frontMatter": {"title": "Introduction", "id": "contributing", "keywords": ["contributing", "guide"], "path": ["/guides/contributing"], "tags": ["contributing", "guide"]}, "sidebar": "docsSideBar", "previous": {"title": "Sound/Audio", "permalink": "/troubleshoot/audio"}, "next": {"title": "Locales Guide", "permalink": "/guides/contributing/locales"}}, {"id": "guides/contributing/locales", "title": "Locales Guide", "description": "Introduction", "source": "@site/docs/guides/contributing/locales.mdx", "sourceDirName": "guides/contributing", "slug": "/guides/contributing/locales", "permalink": "/guides/contributing/locales", "draft": false, "unlisted": false, "tags": [{"inline": true, "label": "locales", "permalink": "/tags/locales"}, {"inline": true, "label": "contributing", "permalink": "/tags/contributing"}, {"inline": true, "label": "guide", "permalink": "/tags/guide"}], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055000, "frontMatter": {"title": "Locales Guide", "id": "locales", "keywords": ["locales", "contributing", "guide"], "path": ["/guides/contributing/locales"], "tags": ["locales", "contributing", "guide"]}, "sidebar": "docsSideBar", "previous": {"title": "Introduction", "permalink": "/guides/contributing/"}}, {"id": "guides/customization/customization-piano", "title": "Piano Customization", "description": "Introduction", "source": "@site/docs/guides/customization/piano_index.mdx", "sourceDirName": "guides/customization", "slug": "/guides/customization/customization-piano", "permalink": "/guides/customization/customization-piano", "draft": false, "unlisted": false, "tags": [{"inline": true, "label": "orchestra", "permalink": "/tags/orchestra"}, {"inline": true, "label": "piano customization", "permalink": "/tags/piano-customization"}, {"inline": true, "label": "customization", "permalink": "/tags/customization"}, {"inline": true, "label": "guide", "permalink": "/tags/guide"}], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055000, "frontMatter": {"title": "Piano Customization", "id": "customization-piano", "keywords": ["piano customization", "orchestra mode", "decals"], "tags": ["orchestra", "piano customization", "customization", "guide"]}, "sidebar": "docsSideBar", "previous": {"title": "Orchestra Mode", "permalink": "/guides/orchestra-mode/"}, "next": {"title": "PRO Subscription", "permalink": "/guides/subscription/"}}, {"id": "guides/general/commands", "title": "Commands", "description": "Chat Commands Documentation", "source": "@site/docs/guides/general/commands.mdx", "sourceDirName": "guides/general", "slug": "/guides/general/commands", "permalink": "/guides/general/commands", "draft": false, "unlisted": false, "tags": [], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055000, "frontMatter": {"id": "commands", "title": "Commands", "keywords": ["commands", "chat commands", "chat", "commands list"]}, "sidebar": "docsSideBar", "previous": {"title": "General", "permalink": "/guides/"}, "next": {"title": "Rooms", "permalink": "/guides/rooms"}}, {"id": "guides/guide", "title": "General", "description": "These are just common guides about the different features of PianoRhythm.", "source": "@site/docs/guides/index.mdx", "sourceDirName": "guides", "slug": "/guides/", "permalink": "/guides/", "draft": false, "unlisted": false, "tags": [], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055000, "sidebarPosition": 1, "frontMatter": {"id": "guide", "sidebar_position": 1, "keywords": ["general terms", "terminologies"]}, "sidebar": "docsSideBar", "next": {"title": "Commands", "permalink": "/guides/general/commands"}}, {"id": "guides/instrument-dock/channel-parameters", "title": "Channel Parameters", "description": "This guide is still a work in progress.", "source": "@site/docs/guides/instrument-dock/channel-parameters.mdx", "sourceDirName": "guides/instrument-dock", "slug": "/guides/instrument-dock/channel-parameters", "permalink": "/guides/instrument-dock/channel-parameters", "draft": false, "unlisted": false, "tags": [{"inline": true, "label": "channel parameters", "permalink": "/tags/channel-parameters"}, {"inline": true, "label": "guide", "permalink": "/tags/guide"}], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055000, "frontMatter": {"title": "Channel Parameters", "id": "channel-parameters", "keywords": ["channel parameters", "channels", "channel volume", "channel panning", "muting channels"], "path": ["/guides/instrument-dock/channel-parameters"], "tags": ["channel parameters", "guide"]}, "sidebar": "docsSideBar", "previous": {"title": "Instrument Dock", "permalink": "/guides/instrument-dock/"}, "next": {"title": "Sheet Music", "permalink": "/guides/sheet-music/"}}, {"id": "guides/instrument-dock/instrument-dock", "title": "Instrument Dock", "description": "This guide is still a work in progress", "source": "@site/docs/guides/instrument-dock/index.mdx", "sourceDirName": "guides/instrument-dock", "slug": "/guides/instrument-dock/", "permalink": "/guides/instrument-dock/", "draft": false, "unlisted": false, "tags": [{"inline": true, "label": "instrument dock", "permalink": "/tags/instrument-dock"}, {"inline": true, "label": "guide", "permalink": "/tags/guide"}], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055000, "frontMatter": {"title": "Instrument Dock", "id": "instrument-dock", "keywords": ["instrument dock", "transpose", "octave", "primary channel", "slot mode", "dock tools"], "path": ["/guides/instrument-dock"], "tags": ["instrument dock", "guide"]}, "sidebar": "docsSideBar", "previous": {"title": "Creating Rooms", "permalink": "/guides/rooms/create-rooms"}, "next": {"title": "Channel Parameters", "permalink": "/guides/instrument-dock/channel-parameters"}}, {"id": "guides/midi-player/midi-player", "title": "MIDI Player", "description": "Introduction", "source": "@site/docs/guides/midi-player/index.mdx", "sourceDirName": "guides/midi-player", "slug": "/guides/midi-player/", "permalink": "/guides/midi-player/", "draft": false, "unlisted": false, "tags": [{"inline": true, "label": "midi", "permalink": "/tags/midi"}, {"inline": true, "label": "guide", "permalink": "/tags/guide"}], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055000, "frontMatter": {"title": "MIDI Player", "id": "midi-player", "keywords": ["midi player", "midi"], "path": ["/guides/midi-player"], "tags": ["midi", "guide"]}, "sidebar": "docsSideBar", "previous": {"title": "Sheet Music", "permalink": "/guides/sheet-music/"}, "next": {"title": "Virtual Piano Player", "permalink": "/guides/midi-player/vp-sequencer"}}, {"id": "guides/midi-player/vp-sequencer", "title": "Virtual Piano Player", "description": "Introduction", "source": "@site/docs/guides/midi-player/vp-sequencer.mdx", "sourceDirName": "guides/midi-player", "slug": "/guides/midi-player/vp-sequencer", "permalink": "/guides/midi-player/vp-sequencer", "draft": false, "unlisted": false, "tags": [{"inline": true, "label": "sequencer", "permalink": "/tags/sequencer"}, {"inline": true, "label": "guide", "permalink": "/tags/guide"}], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055000, "frontMatter": {"title": "Virtual Piano Player", "id": "vp-sequencer", "keywords": ["virtual piano sequencer", "sequencer"], "path": ["/guides/vp-sequencer"], "tags": ["sequencer", "guide"]}, "sidebar": "docsSideBar", "previous": {"title": "MIDI Player", "permalink": "/guides/midi-player/"}, "next": {"title": "<PERSON><PERSON>", "permalink": "/guides/components/looper"}}, {"id": "guides/orchestra-mode/orchestra-mode", "title": "Orchestra Mode", "description": "Introduction", "source": "@site/docs/guides/orchestra-mode/index.mdx", "sourceDirName": "guides/orchestra-mode", "slug": "/guides/orchestra-mode/", "permalink": "/guides/orchestra-mode/", "draft": false, "unlisted": false, "tags": [{"inline": true, "label": "orchestra", "permalink": "/tags/orchestra"}, {"inline": true, "label": "guide", "permalink": "/tags/guide"}], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055000, "frontMatter": {"title": "Orchestra Mode", "id": "orchestra-mode", "keywords": ["orchestra mode", "orchestra"], "path": ["/guides/orchestra-mode"], "tags": ["orchestra", "guide"]}, "sidebar": "docsSideBar", "previous": {"title": "<PERSON><PERSON>", "permalink": "/guides/components/looper"}, "next": {"title": "Piano Customization", "permalink": "/guides/customization/customization-piano"}}, {"id": "guides/rooms", "title": "Rooms", "description": "What are Rooms in PianoRhythm?", "source": "@site/docs/guides/rooms-index.mdx", "sourceDirName": "guides", "slug": "/guides/rooms", "permalink": "/guides/rooms", "draft": false, "unlisted": false, "tags": [{"inline": true, "label": "rooms", "permalink": "/tags/rooms"}, {"inline": true, "label": "guide", "permalink": "/tags/guide"}], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055000, "frontMatter": {"title": "Rooms", "id": "rooms", "tags": ["rooms", "guide"]}, "sidebar": "docsSideBar", "previous": {"title": "Commands", "permalink": "/guides/general/commands"}, "next": {"title": "Creating Rooms", "permalink": "/guides/rooms/create-rooms"}}, {"id": "guides/rooms/create-rooms", "title": "Creating Rooms", "description": "The New Room Modal in PianoRhythm allows you to create or update a room with various settings.", "source": "@site/docs/guides/rooms/creating-rooms.mdx", "sourceDirName": "guides/rooms", "slug": "/guides/rooms/create-rooms", "permalink": "/guides/rooms/create-rooms", "draft": false, "unlisted": false, "tags": [{"inline": true, "label": "rooms", "permalink": "/tags/rooms"}, {"inline": true, "label": "new room", "permalink": "/tags/new-room"}, {"inline": true, "label": "create room", "permalink": "/tags/create-room"}, {"inline": true, "label": "update room", "permalink": "/tags/update-room"}, {"inline": true, "label": "guide", "permalink": "/tags/guide"}], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055000, "frontMatter": {"title": "Creating Rooms", "id": "create-rooms", "keywords": ["rooms", "new room", "create room", "update room", "guide"], "tags": ["rooms", "new room", "create room", "update room", "guide"]}, "sidebar": "docsSideBar", "previous": {"title": "Rooms", "permalink": "/guides/rooms"}, "next": {"title": "Instrument Dock", "permalink": "/guides/instrument-dock/"}}, {"id": "guides/sequencer/midi-step-sequencer", "title": "MIDI Step Sequencer", "description": "Introduction", "source": "@site/docs/guides/sequencer/index.mdx", "sourceDirName": "guides/sequencer", "slug": "/guides/sequencer/", "permalink": "/guides/sequencer/", "draft": false, "unlisted": false, "tags": [{"inline": true, "label": "step sequencer", "permalink": "/tags/step-sequencer"}, {"inline": true, "label": "guide", "permalink": "/tags/guide"}], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055000, "frontMatter": {"title": "MIDI Step Sequencer", "id": "midi-step-sequencer", "keywords": ["step sequencer", "midi sequencer"], "path": ["/guides/sequencer"], "tags": ["step sequencer", "guide"]}}, {"id": "guides/sheet-music/sheetmusic", "title": "Sheet Music", "description": "Introduction", "source": "@site/docs/guides/sheet-music/index.mdx", "sourceDirName": "guides/sheet-music", "slug": "/guides/sheet-music/", "permalink": "/guides/sheet-music/", "draft": false, "unlisted": false, "tags": [{"inline": true, "label": "sheet music", "permalink": "/tags/sheet-music"}, {"inline": true, "label": "guide", "permalink": "/tags/guide"}], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055000, "frontMatter": {"title": "Sheet Music", "id": "sheetmusic", "keywords": ["sheet music", "submit sheet music", "upload sheet music"], "path": ["/guides/sheet-music"], "tags": ["sheet music", "guide"]}, "sidebar": "docsSideBar", "previous": {"title": "Channel Parameters", "permalink": "/guides/instrument-dock/channel-parameters"}, "next": {"title": "MIDI Player", "permalink": "/guides/midi-player/"}}, {"id": "guides/subscription/pro-subscription", "title": "PRO Subscription", "description": "General", "source": "@site/docs/guides/subscription/index.mdx", "sourceDirName": "guides/subscription", "slug": "/guides/subscription/", "permalink": "/guides/subscription/", "draft": false, "unlisted": false, "tags": [{"inline": true, "label": "pro subscription", "permalink": "/tags/pro-subscription"}, {"inline": true, "label": "pro", "permalink": "/tags/pro"}, {"inline": true, "label": "guide", "permalink": "/tags/guide"}], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055000, "frontMatter": {"title": "PRO Subscription", "id": "pro-subscription", "keywords": ["subscription", "pro", "pro subscription"], "path": ["/guides/pro-subscription"], "tags": ["pro subscription", "pro", "guide"]}, "sidebar": "docsSideBar", "previous": {"title": "Piano Customization", "permalink": "/guides/customization/customization-piano"}, "next": {"title": "Getting Started", "permalink": "/tutorials/"}}, {"id": "troubleshoot/audio", "title": "Sound/Audio", "description": "General", "source": "@site/docs/troubleshoot/troubleshoot-audio.mdx", "sourceDirName": "troubleshoot", "slug": "/troubleshoot/audio", "permalink": "/troubleshoot/audio", "draft": false, "unlisted": false, "tags": [{"inline": true, "label": "sound", "permalink": "/tags/sound"}, {"inline": true, "label": "audio", "permalink": "/tags/audio"}, {"inline": true, "label": "piano", "permalink": "/tags/piano"}, {"inline": true, "label": "troubleshoot", "permalink": "/tags/troubleshoot"}], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055000, "frontMatter": {"id": "audio", "title": "Sound/Audio", "authors": "oak", "tags": ["sound", "audio", "piano", "troubleshoot"], "keywords": ["troubleshoot", "troubleshoot audio", "troubleshoot sound"]}, "sidebar": "docsSideBar", "previous": {"title": "Plugins", "permalink": "/advanced-guides/plugins/"}, "next": {"title": "Introduction", "permalink": "/guides/contributing/"}}, {"id": "tutorials/tutorial-instruments", "title": "Changing Instruments", "description": "A soundfont is typically comprised of a collection of instruments or technically called presets.", "source": "@site/docs/tutorials/tut-instruments.mdx", "sourceDirName": "tutorials", "slug": "/tutorials/tutorial-instruments", "permalink": "/tutorials/tutorial-instruments", "draft": false, "unlisted": false, "tags": [{"inline": true, "label": "tutorial", "permalink": "/tags/tutorial"}, {"inline": true, "label": "instruments", "permalink": "/tags/instruments"}], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055000, "frontMatter": {"title": "Changing Instruments", "id": "tutorial-instruments", "keywords": ["tutorial", "instruments", "load instrument", "change instrument", "instruments list"], "path": ["/tutorials/tutorial-instruments"], "tags": ["tutorial", "instruments"]}, "sidebar": "docsSideBar", "previous": {"title": "Getting Started", "permalink": "/tutorials/"}, "next": {"title": "Loading Soundfonts", "permalink": "/tutorials/tutorial-soundfonts"}}, {"id": "tutorials/tutorial-self-host-rooms", "title": "Self Hosted Rooms", "description": "Self hosting rooms is currently not available.", "source": "@site/docs/tutorials/tut-selfhost-room.mdx", "sourceDirName": "tutorials", "slug": "/tutorials/tutorial-self-host-rooms", "permalink": "/tutorials/tutorial-self-host-rooms", "draft": false, "unlisted": false, "tags": [{"inline": true, "label": "self host", "permalink": "/tags/self-host"}, {"inline": true, "label": "self host rooms", "permalink": "/tags/self-host-rooms"}, {"inline": true, "label": "host", "permalink": "/tags/host"}], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055000, "frontMatter": {"title": "Self Hosted Rooms", "id": "tutorial-self-host-rooms", "keywords": ["hosting", "host room", "self hosting", "self-host", "self host"], "path": ["/tutorials/tutorial-self-host-rooms"], "tags": ["self host", "self host rooms", "host"]}, "sidebar": "docsSideBar", "previous": {"title": "Loading Soundfonts", "permalink": "/tutorials/tutorial-soundfonts"}, "next": {"title": "Plugins", "permalink": "/advanced-guides/plugins/"}}, {"id": "tutorials/tutorial-soundfonts", "title": "Loading Soundfonts", "description": "First of all, what is a soundfont? Well, according to Wikipedia:", "source": "@site/docs/tutorials/tut-load-soundfont.mdx", "sourceDirName": "tutorials", "slug": "/tutorials/tutorial-soundfonts", "permalink": "/tutorials/tutorial-soundfonts", "draft": false, "unlisted": false, "tags": [{"inline": true, "label": "tutorial", "permalink": "/tags/tutorial"}, {"inline": true, "label": "soundfonts", "permalink": "/tags/soundfonts"}], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055000, "frontMatter": {"title": "Loading Soundfonts", "id": "tutorial-soundfonts", "keywords": ["soundfonts", "load soundfont", "custom soundfont", "tutorial"], "path": ["/tutorials/tutorial-soundfonts"], "tags": ["tutorial", "soundfonts"]}, "sidebar": "docsSideBar", "previous": {"title": "Changing Instruments", "permalink": "/tutorials/tutorial-instruments"}, "next": {"title": "Self Hosted Rooms", "permalink": "/tutorials/tutorial-self-host-rooms"}}, {"id": "tutorials/tutorials", "title": "Getting Started", "description": "Wondering how to do a certain task or thing in PianoRhythm?", "source": "@site/docs/tutorials/index.mdx", "sourceDirName": "tutorials", "slug": "/tutorials/", "permalink": "/tutorials/", "draft": false, "unlisted": false, "tags": [], "version": "current", "lastUpdatedBy": "Author", "lastUpdatedAt": 1539502055000, "frontMatter": {"title": "Getting Started", "id": "tutorials"}, "sidebar": "docsSideBar", "previous": {"title": "PRO Subscription", "permalink": "/guides/subscription/"}, "next": {"title": "Changing Instruments", "permalink": "/tutorials/tutorial-instruments"}}], "drafts": [], "sidebars": {"docsSideBar": [{"type": "category", "label": "Guides", "items": [{"type": "category", "label": "General", "link": {"type": "doc", "id": "guides/guide"}, "items": [{"type": "doc", "id": "guides/general/commands"}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Rooms", "link": {"type": "doc", "id": "guides/rooms"}, "items": [{"type": "doc", "id": "guides/rooms/create-rooms"}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Instrument Dock", "link": {"type": "doc", "id": "guides/instrument-dock/instrument-dock"}, "items": [{"type": "doc", "id": "guides/instrument-dock/channel-parameters"}], "collapsed": true, "collapsible": true}, {"type": "doc", "id": "guides/sheet-music/sheetmusic"}, {"type": "category", "label": "MIDI Player", "link": {"type": "doc", "id": "guides/midi-player/midi-player"}, "items": [{"type": "doc", "id": "guides/midi-player/vp-sequencer"}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Audio Components", "items": [{"type": "doc", "id": "guides/components/looper"}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Orchestra Mode", "link": {"type": "doc", "id": "guides/orchestra-mode/orchestra-mode"}, "items": [{"type": "doc", "id": "guides/customization/customization-piano"}], "collapsed": true, "collapsible": true}, {"type": "doc", "id": "guides/subscription/pro-subscription"}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Tutorials", "items": [{"type": "doc", "id": "tutorials/tutorials"}, {"type": "doc", "id": "tutorials/tutorial-instruments"}, {"type": "doc", "id": "tutorials/tutorial-soundfonts"}, {"type": "doc", "id": "tutorials/tutorial-self-host-rooms"}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Advanced Tutorials", "items": [{"type": "doc", "id": "advanced-guides/plugins/plugins"}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Troubleshooting", "items": [{"type": "doc", "id": "troubleshoot/audio"}], "collapsed": true, "collapsible": true}, {"type": "category", "label": "Contributing", "items": [{"type": "doc", "id": "guides/contributing/contributing"}, {"type": "doc", "id": "guides/contributing/locales"}], "collapsed": true, "collapsible": true}], "communitySidebar": [{"type": "doc", "id": "community/community"}, {"type": "doc", "id": "community/credits"}, {"type": "category", "label": "Development", "collapsible": true, "collapsed": true, "items": [{"type": "category", "label": "Architecture", "collapsible": true, "collapsed": true, "items": [{"type": "doc", "label": "Client", "id": "community/development/architecture/client/architecture-client"}], "link": {"type": "doc", "id": "community/development/architecture/dev-architecture"}}, {"type": "doc", "label": "Gallery", "id": "community/development/gallery/gallery"}], "link": {"type": "doc", "id": "community/development/development"}}, {"type": "doc", "id": "community/discord"}], "developmentSidebar": [{"type": "doc", "label": "Gallery", "id": "development/gallery/gallery"}, {"type": "doc", "id": "development/development"}]}}]}}, "docusaurus-plugin-content-blog": {"blog": {"blogSidebarTitle": "Recent posts", "blogPosts": [{"id": "back-from-hiatus", "metadata": {"permalink": "/blog/back-from-hiatus", "source": "@site/blog/2025-06-28-back-from-hiatus/index.md", "title": "I'm Back! PianoRhythm v0.10.0 Coming Soon", "description": "Hey everyone! <PERSON> here, and I'm excited to announce that I'm back to actively working on PianoRhythm after several months of hiatus.", "date": "2025-06-28T00:00:00.000Z", "tags": [{"inline": true, "label": "oak", "permalink": "/blog/tags/oak"}, {"inline": true, "label": "pianorhythm", "permalink": "/blog/tags/pianorhythm"}, {"inline": true, "label": "hiatus", "permalink": "/blog/tags/hiatus"}, {"inline": true, "label": "v0.10.0", "permalink": "/blog/tags/v-0-10-0"}, {"inline": true, "label": "comeback", "permalink": "/blog/tags/comeback"}, {"inline": true, "label": "staging", "permalink": "/blog/tags/staging"}], "readingTime": 2.6, "hasTruncateMarker": true, "authors": [{"name": "Oak", "title": "PianoRhythm Creator/Developer", "url": "<EMAIL>", "key": "oak", "page": null}], "frontMatter": {"slug": "back-from-hiatus", "title": "I'm Back! PianoRhythm v0.10.0 Coming Soon", "authors": ["oak"], "tags": ["oak", "pianorhythm", "hiatus", "v0.10.0", "comeback", "staging"]}, "unlisted": false, "nextItem": {"title": "Server Upgrade", "permalink": "/blog/update_2023"}}, "content": "Hey everyone! Oak here, and I'm excited to announce that I'm back to actively working on PianoRhythm after several months of hiatus.\n\nI know many of you have been wondering what's been going on, and I wanted to take a moment to update you all on where things stand and what's coming next.\n\n{/* truncate */}\n\n## Where I've Been\n\nLife has a way of throwing curveballs, and the past several months have been no exception for me. Between work commitments, personal matters, and the need to step back and recharge, I had to take some time away from active PianoRhythm development. \n\nI know this might have been frustrating for some of you who were eagerly waiting for updates, and I truly appreciate your patience and continued support during this time. The PianoRhythm community has always been incredible, and knowing that you've stuck around means the world to me.\n\n## What's Coming: v0.10.0\n\nI'm thrilled to share that I'm back in full development mode, and I have some exciting news: **PianoRhythm v0.10.0 is planned for release around August 2025!**\n\nThis upcoming version represents a significant milestone in PianoRhythm's journey. While I can't reveal all the details just yet, I can tell you that v0.10.0 will include:\n\n- (Potential) Performance improvements and bug fixes\n- Continued refinements to the audio engine\n- UI/UX improvements\n- Dedicated page for the sheet music repository\n- And more!\n\nI'll be sharing more specific details about the features and improvements as we get closer to the release date.\n\n## Check Out the Staging Site\n\nFor those of you who want to get a sneak peek at what's coming, you can check out the latest development version on our **staging site** at [https://staging.pianorhythm.io](https://staging.pianorhythm.io). This is where I test new features and improvements before they make it to the main application.\n\nKeep in mind that the staging site is for testing purposes, so you might encounter some bugs or incomplete features. But it's a great way to see the direction PianoRhythm is heading and provide feedback on new developments.\n\n## Moving Forward\n\nI'm committed to being more consistent with development and communication moving forward. The break, while necessary, has given me renewed energy and perspective on PianoRhythm's future.\n\nI'll be posting more regular updates here on the blog and staying more active in the community. Your feedback, suggestions, and bug reports are invaluable in making PianoRhythm the best it can be.\n\n## Thank You\n\nBefore I wrap up, I want to give a huge thank you to everyone who has continued to support PianoRhythm during my absence. Whether you've been playing regularly, sharing the app with friends, or just patiently waiting for updates - you're the reason I'm motivated to keep building and improving this platform.\n\nPianoRhythm has always been a passion project, and seeing how much it means to the community makes all the hard work worthwhile.\n\nStay tuned for more updates as we approach the v0.10.0 release. I'm excited to share this journey with all of you!\n\nAs always, you can reach me at:\n- Email: <EMAIL>\n- Discord: Feel free to ping me in the PianoRhythm Discord server\n\nLet's make some beautiful music together! 🎹\n\n— Oak"}, {"id": "update_2023", "metadata": {"permalink": "/blog/update_2023", "source": "@site/blog/2023-12-30/index.md", "title": "Server Upgrade", "description": "Whew, it's been a while since I've posted here. I've been busy with life and other things, but I've finally gotten around to upgrading the server. Version 0.9.0 has now been released! I've also added a few new features to the app. Let's dive in, shall we?", "date": "2023-12-30T00:00:00.000Z", "tags": [{"inline": true, "label": "oak", "permalink": "/blog/tags/oak"}, {"inline": true, "label": "pianorhythm", "permalink": "/blog/tags/pianorhythm"}, {"inline": true, "label": "server upgrade", "permalink": "/blog/tags/server-upgrade"}], "readingTime": 2.57, "hasTruncateMarker": true, "authors": [{"name": "Oak", "title": "PianoRhythm Creator/Developer", "url": "<EMAIL>", "key": "oak", "page": null}], "frontMatter": {"slug": "update_2023", "title": "Server Upgrade", "authors": ["oak"], "tags": ["oak", "pianorhythm", "server upgrade"]}, "unlisted": false, "prevItem": {"title": "I'm Back! PianoRhythm v0.10.0 Coming Soon", "permalink": "/blog/back-from-hiatus"}, "nextItem": {"title": "Welcome", "permalink": "/blog/welcome"}}, "content": "Whew, it's been a while since I've posted here. I've been busy with life and other things, but I've finally gotten around to upgrading the server. Version `0.9.0` has now been released! I've also added a few new features to the app. Let's dive in, shall we?\r\n\r\n{/* truncate */}\r\n\r\n### Server Upgrade\r\nI've been on quite an adventure with the development of the server. Initially, it was built using F# and the Akka.NET framework. However, as the complexity grew, I found the backend becoming a bit too challenging to maintain. Also, the server didn't seem to be as performant as I would have liked. So I hope many of the weird bugs that you've encountered in the past, due to the server, are now gone with this new update.\r\n\r\nSo, I decided to give Rust a try. Rust, with the Actix framework, offered a fresh start and a chance to streamline the codebase. I've been diving deep into Rust lately, and I've been enjoying every minute of it. The decision to rewrite the backend in Rust was a significant one, but it's a decision I'm glad I made.\r\n\r\nHowever, it's not a complete rewrite. There are still certain prior features that I need to rewrite. For example, the backend for the sheet music repo service needs to be ported into Rust. I hope to get that done in the following weeks.\r\n\r\nThe transition to Rust has not only made the backend easier to maintain but also more efficient at processing web requests. Plus, it's opened up a whole new world of possibilities for adding new features. I'm excited about the improvements I've made and even more excited about what's to come.\r\n\r\nStay tuned for more updates as I continue to enhance the app with new features!\r\n\r\n### New Features\r\n\r\nIn the recent development cycle, I was also able to add/improve some features.\r\n\r\n- Added two new stages: \"Music Studio\" and \"Arena.\"\r\n- Added stage sound effects. You can now play background music while playing the piano.\r\n  - Rain, Wind, and Bird audio effects.\r\n- Moderators are now able to add/remove badges to/from users. With that being said, I would like to add more moderators to the team. If you're interested, please send me a message or you can fill out this form: [Application Form](https://form.jotform.com/240056814147150).\r\n-  I've implemented a system to create a new lobby when a user is attempting to join a lobby that is full.\r\n\r\n### Future Plans\r\n\r\nWith the release of version `0.9.0`, we're reaching the near end of the beta phase. If all goes well with this new server upgradeg, then I plan on releasing version `1.0.0` in the coming months.\r\n\r\nThe major focuses for the next few months will be:\r\n- Improving the UI/UX.\r\n- Add a Midi Music repository.\r\n- Adding more social features.\r\n- Adding more exclusive features for the PRO subscription plan.\r\n- Stabilize the self-hosting process.\r\n- Improving mobile support.\r\n- Improving localization support _(Send me a message if you would like to contribute. You'll get a translator badge as recognizition for your efforts)_.\r\n- Add more DAW like features.\r\n- Bring back and improve Avatars."}, {"id": "welcome", "metadata": {"permalink": "/blog/welcome", "source": "@site/blog/2023-05-02-welcome/index.md", "title": "Welcome", "description": "Welcome to PianoRhythm's first blog post!", "date": "2023-05-02T00:00:00.000Z", "tags": [{"inline": true, "label": "welcome", "permalink": "/blog/tags/welcome"}, {"inline": true, "label": "oak", "permalink": "/blog/tags/oak"}, {"inline": true, "label": "pianorhythm", "permalink": "/blog/tags/pianorhythm"}, {"inline": true, "label": "history", "permalink": "/blog/tags/history"}, {"inline": true, "label": "v2", "permalink": "/blog/tags/v-2"}, {"inline": true, "label": "v2 history", "permalink": "/blog/tags/v-2-history"}, {"inline": true, "label": "v3", "permalink": "/blog/tags/v-3"}], "readingTime": 9.24, "hasTruncateMarker": true, "authors": [{"name": "Oak", "title": "PianoRhythm Creator/Developer", "url": "<EMAIL>", "key": "oak", "page": null}], "frontMatter": {"slug": "welcome", "title": "Welcome", "authors": ["oak"], "tags": ["welcome", "oak", "pianorhythm", "history", "v2", "v2 history", "v3"]}, "unlisted": false, "prevItem": {"title": "Server Upgrade", "permalink": "/blog/update_2023"}}, "content": "Welcome to PianoRhythm's first blog post!\n\nYes, it is <PERSON>, <PERSON>! I am the creator and main developer of PianoRhythm.\n\nIf you're looking to create a blog post pertaining to PianoRhythm, piano, or just anything music related, then feel free to contact me!\n\n- Email: <EMAIL>\n- Discord: Oak#9806\n\nYou can find the PianoRhythm app's url [here](https://pianorhythm.io).\n\n{/* truncate */}\n\n### Why did I create PianoRhythm in the first place?\nWell, like many of you, I was an avid user of MPP and loved it. At the time, I just got into learning the piano and actual programming. I thought MPP had so much potential and the lack of updates kind of pushed me into envisioning my own thing. PianoRhythm is just a passion product. I didn't create it for the profit or for the masses.\n\nIt's something I've been putting my own time and money cause I just really enjoy creating and didn't mind sharing the platform with other people. I'm not trying to compete with MPP. I wanted to PianoRhythm to be its own thing with a unique world. I'm still striving for that. So far, I've been the sole developer with some help from Bop<PERSON><PERSON> on the Discord stuff in the backend. I can only do so much as a working professional.\n\n### What happened to v2?\nSo v2 was created back in 2016 when I just started learning programming in college. I definitely used MPP as the basis for the idea but used that as an opportunity to try different things. v2 was generally stable because it was simpler and less demanding. And that's because I was initially following MPP's footsteps. But for those OG members who may remember, that I did have a 3D mode in v2. However, what was the common complaint? That it was too laggy for lower performing machines.\n\nSo, I scrapped that idea in the mean time to focus. But, I really really preferred the aesthetics of 3D. The way I justify it is that if you really want 2d, then you have MPP for that. May not be ideal but there's nothing stopping you from using both (free) services.\n\nI wanted PR to be different. What's the point of creating a clone of MPP with just a different skin? The features that were in v2 were ideas I just wanted to play around with that I thought people would use. So, like being able to record, have multiple instruments, have a personal representation with avatars (blobs), and other stuff. I also wanted to make PR more game like because I've always been interested in making games as well. v2 was always in early alpha state since there was a lot of active development. But the ideas that I had in mind were still limited by certain technological limits that I'll talk about later.\n\nAnyhoo, I was in college back then and my time was limited. Since I was still learning of heck of a lot of stuff about development, there was a lot of sphaghetti code and probably bad practices. Add also that here were plenty of hiatuses that occurred and over time, things became unmaintable.\nOnce I got an internship at my current workplace, I learned a lot of new stuff and decided to create the project from scratch with my new knowledge.\nThus came v3. The eventual plan was to get v3 to decent enough state with similar features to v2 and have it replace it. Before I officially replaced v2, I did have message that would show in v2's lobby about me working on v3 so hopefully it wasn't that much of a surprise. And that was there for at least a few months.\n\n### What's up with v3?\nv3 has an interesting history because I've probably rewritten it from scratch like a dozen times. But before I get to some of the history, I'll first answer a few common things people have said:\n\n#### Why is V3 like forced 3D? It's so laggy!\nLike I mentioned above, that is the direction I've wanted to take and I knew it was going to outcast some users. It's impossible to please everyone so I'm not trying to. I'm still learning so I will keep trying to optimizing the app as best as I can but there's only so much old hardware can deal with. My machine is not a beast (GTX 1080 GPU and i7 CPU) but it runs perfectly smooth so far. Unfortunately, it's not like I have a bunch of old laptops sitting around where I can do performance tests. Keep in mind that v3 is in open beta and in active development. This where you guys come in and provide feedback so I can best try and deal with these issues. In general, v3 has a totally different tech stack from v2. Why? Cause I want to learn and try different things. If you've created any software, you would know how rapid frameworks, libraries, and other tech can get.\n\nHonestly, I would've like to use a proper 3D game engine to make PianoRhythm but for now, web developing is relatively easier and has better cross platform compatibility. I'm primarily limited to web technology. The desktop app (and no, it's actually not using Electron but actually using a product called Tauri (https://tauri.app/) that renders using the machine's native webview), was an attempt to at least provide more stability and a better desktop app experience.\n\n#### What's up v3's audio engine? It's terrible.\nHmm, that's primarily subjective in my opinion. So, I'll cite some sources and the reasoning behind certain things with the audio engine.\nI knew people were going to probably bash v3's audio since they were most likely used to v2's audio. **A lot of people don't like change**. Sure, there's probably a distinctive difference in audio fidelity but I imagine if you never used v2 in the first place and came to v3, then you would've probably had a different view.\n\nLuckily, if you don't like v3's audio, you can always use the midi output to play audio on your preferred synthesizer of choice. And once again, this is in active development where there's always room for improvement. If you're a developer that can build a better audio engine, then please let me know. I'm just one person working on the front and back end.\n\nFirst, I wanted PR to be able to use multiple instruments and thus allowing other users to hear such instruments to have a band/orchestra like experience. MPO (multiplayer orchestra) pretty much tackled that.\n\nv2's audio engine was using an existing tech that converted audio samples to a base64 encoded javascript object and allowed to process those encoded samples through WebAudio (https://github.com/gleitz/midi-js-soundfonts).\nSo an example would like look:\n\n```js\nMIDI.Soundfont.high_quality_acoustic_grand_piano = {\n    \"A0\": \"data:audio/ogg;base64,SUQzAwAAAAAAOlRYWFgAAAAW...\"\n}\n```\n\nWas it the best choice back then? Who knows? I was still learning\nand it was relatively easy to use. However, these objects were per instrument and took a bit of computer memory. So, each instrument had to manually be loaded into the browser to be able to be played. Okay so what's the issue? Well, if I was going to provide a feature to allow using different instruments, most people would expect to able to hear other users' instruments. That would be fine and dandy if it was just a few instruments but these audio js objects were converted from .sf2 soundfonts. These are files that are sample based audio files that can contain multiple instruments and sound effects. These have been standardized and a GM (General Midi) usually contain at least 100+ instruments.\n\nI recall that I experimented trying to load every single JavaScript audio object and quickly ran into memory limitation issues (like 2GB used for the audio) with Chrome. Often times, the browsers limits what an active site can do. So from a design aspect, I couldn't really justify users kind of forcing other users to load these audio objects with how much it cost.\n\nI had to think of an alternative and that's where using soundfont files (.sf2) came to mind. Theoretically, the tech behind them seem to be effecient and really what I was looking for. A 10mb soundfont file could potentially have all the GM instruments loaded without costing a lot in RAM. Also, there would be added benefit of people using their own custom soundfonts if they didn't like the default ones that PR might have. Win/Win, right? At the time of v2, I don't recall of any stable libraries that supported loading sf2 files.\n\nI'm no audio engineer so I definitely was going to try and build one from scratch since it was way out of my scope. But, I didn't give up. That's why with v3, I did so many rewrites trying a lot of different things. Developing is hard. I'm not a genius. Just an average programmer that's trying his best.\n\nOnce better technology came about, I finally decided to go the Web WASM route and found a SF2 parsing library written in Rust called OxiSynth (https://github.com/PolyMeilex/OxiSynth). This was also an opportunity to learn a new programming language (developers like shiny new stuff).\n\nSo far, I like this library. Since I didn't write it from scratch, I don't know the ins and outs, yet. There's a lot of low level audio programming that I have to get familiar with and that will take time.  Now if users have the same soundfont, you can load any instrument and have it heard by anyone without any additional cost.\n\nUsers also now have an option to use higher quality soundfonts without any major real drawback (their machine is the only limitation, I suppose). I still have to figure out how to allow users to hear a custom soundfont that someone else loaded (I do not want to force someone to have to download a 2gb soundfont for example). And speaking of size, choosing a default soundfont was a compromise. Sure, I could have a high quality one as the default but the default but that would incur a large download. So, I initially settled on a standard GM soundfont that was around 50-60mb that sounded decent (at least to me). However, it was recently pushed to like 100ish since people really wanted the old v2 default piano (which is about 50+ mb decoded in audio samples). So, I learned to created soundfonts and replaced the first piano in the default with v2's.\n\nI recently added a first draft of an equalizer for those who want to try and fine tune the audio. The point is that I'm trying my best but I can't please everyone. So, if you have supported me thus far, I really appreciate it. There's been plenty of times where I've wanted to quit and just completely abandon the project. As a creator, open criticism is expected and I can deal with. But I can definitely do without people who are just extremely negative with nothing to offer."}], "blogListPaginated": [{"items": ["back-from-hiatus", "update_2023", "welcome"], "metadata": {"permalink": "/blog", "page": 1, "postsPerPage": 10, "totalPages": 1, "totalCount": 3, "blogDescription": "A blog about the PianoRhythm app and its development.", "blogTitle": "PianoRhythm Blog"}}], "blogTags": {"/blog/tags/oak": {"inline": true, "label": "oak", "permalink": "/blog/tags/oak", "items": ["back-from-hiatus", "update_2023", "welcome"], "pages": [{"items": ["back-from-hiatus", "update_2023", "welcome"], "metadata": {"permalink": "/blog/tags/oak", "page": 1, "postsPerPage": 10, "totalPages": 1, "totalCount": 3, "blogDescription": "A blog about the PianoRhythm app and its development.", "blogTitle": "PianoRhythm Blog"}}], "unlisted": false}, "/blog/tags/pianorhythm": {"inline": true, "label": "pianorhythm", "permalink": "/blog/tags/pianorhythm", "items": ["back-from-hiatus", "update_2023", "welcome"], "pages": [{"items": ["back-from-hiatus", "update_2023", "welcome"], "metadata": {"permalink": "/blog/tags/pianorhythm", "page": 1, "postsPerPage": 10, "totalPages": 1, "totalCount": 3, "blogDescription": "A blog about the PianoRhythm app and its development.", "blogTitle": "PianoRhythm Blog"}}], "unlisted": false}, "/blog/tags/hiatus": {"inline": true, "label": "hiatus", "permalink": "/blog/tags/hiatus", "items": ["back-from-hiatus"], "pages": [{"items": ["back-from-hiatus"], "metadata": {"permalink": "/blog/tags/hiatus", "page": 1, "postsPerPage": 10, "totalPages": 1, "totalCount": 1, "blogDescription": "A blog about the PianoRhythm app and its development.", "blogTitle": "PianoRhythm Blog"}}], "unlisted": false}, "/blog/tags/v-0-10-0": {"inline": true, "label": "v0.10.0", "permalink": "/blog/tags/v-0-10-0", "items": ["back-from-hiatus"], "pages": [{"items": ["back-from-hiatus"], "metadata": {"permalink": "/blog/tags/v-0-10-0", "page": 1, "postsPerPage": 10, "totalPages": 1, "totalCount": 1, "blogDescription": "A blog about the PianoRhythm app and its development.", "blogTitle": "PianoRhythm Blog"}}], "unlisted": false}, "/blog/tags/comeback": {"inline": true, "label": "comeback", "permalink": "/blog/tags/comeback", "items": ["back-from-hiatus"], "pages": [{"items": ["back-from-hiatus"], "metadata": {"permalink": "/blog/tags/comeback", "page": 1, "postsPerPage": 10, "totalPages": 1, "totalCount": 1, "blogDescription": "A blog about the PianoRhythm app and its development.", "blogTitle": "PianoRhythm Blog"}}], "unlisted": false}, "/blog/tags/staging": {"inline": true, "label": "staging", "permalink": "/blog/tags/staging", "items": ["back-from-hiatus"], "pages": [{"items": ["back-from-hiatus"], "metadata": {"permalink": "/blog/tags/staging", "page": 1, "postsPerPage": 10, "totalPages": 1, "totalCount": 1, "blogDescription": "A blog about the PianoRhythm app and its development.", "blogTitle": "PianoRhythm Blog"}}], "unlisted": false}, "/blog/tags/server-upgrade": {"inline": true, "label": "server upgrade", "permalink": "/blog/tags/server-upgrade", "items": ["update_2023"], "pages": [{"items": ["update_2023"], "metadata": {"permalink": "/blog/tags/server-upgrade", "page": 1, "postsPerPage": 10, "totalPages": 1, "totalCount": 1, "blogDescription": "A blog about the PianoRhythm app and its development.", "blogTitle": "PianoRhythm Blog"}}], "unlisted": false}, "/blog/tags/welcome": {"inline": true, "label": "welcome", "permalink": "/blog/tags/welcome", "items": ["welcome"], "pages": [{"items": ["welcome"], "metadata": {"permalink": "/blog/tags/welcome", "page": 1, "postsPerPage": 10, "totalPages": 1, "totalCount": 1, "blogDescription": "A blog about the PianoRhythm app and its development.", "blogTitle": "PianoRhythm Blog"}}], "unlisted": false}, "/blog/tags/history": {"inline": true, "label": "history", "permalink": "/blog/tags/history", "items": ["welcome"], "pages": [{"items": ["welcome"], "metadata": {"permalink": "/blog/tags/history", "page": 1, "postsPerPage": 10, "totalPages": 1, "totalCount": 1, "blogDescription": "A blog about the PianoRhythm app and its development.", "blogTitle": "PianoRhythm Blog"}}], "unlisted": false}, "/blog/tags/v-2": {"inline": true, "label": "v2", "permalink": "/blog/tags/v-2", "items": ["welcome"], "pages": [{"items": ["welcome"], "metadata": {"permalink": "/blog/tags/v-2", "page": 1, "postsPerPage": 10, "totalPages": 1, "totalCount": 1, "blogDescription": "A blog about the PianoRhythm app and its development.", "blogTitle": "PianoRhythm Blog"}}], "unlisted": false}, "/blog/tags/v-2-history": {"inline": true, "label": "v2 history", "permalink": "/blog/tags/v-2-history", "items": ["welcome"], "pages": [{"items": ["welcome"], "metadata": {"permalink": "/blog/tags/v-2-history", "page": 1, "postsPerPage": 10, "totalPages": 1, "totalCount": 1, "blogDescription": "A blog about the PianoRhythm app and its development.", "blogTitle": "PianoRhythm Blog"}}], "unlisted": false}, "/blog/tags/v-3": {"inline": true, "label": "v3", "permalink": "/blog/tags/v-3", "items": ["welcome"], "pages": [{"items": ["welcome"], "metadata": {"permalink": "/blog/tags/v-3", "page": 1, "postsPerPage": 10, "totalPages": 1, "totalCount": 1, "blogDescription": "A blog about the PianoRhythm app and its development.", "blogTitle": "PianoRhythm Blog"}}], "unlisted": false}}, "blogTagsListPath": "/blog/tags", "authorsMap": {"oak": {"name": "Oak", "title": "PianoRhythm Creator/Developer", "url": "<EMAIL>", "key": "oak", "page": null}}}}, "docusaurus-plugin-content-pages": {"default": [{"type": "jsx", "permalink": "/", "source": "@site/src/pages/index.js"}]}, "docusaurus-plugin-debug": {}, "docusaurus-plugin-svgr": {}, "docusaurus-theme-classic": {}, "docusaurus-tailwindcss": {}, "dev-webpack-configure": {}, "docusaurus-lunr-search": {}, "changelog-plugin": {"changelog": {"blogSidebarTitle": "Changelog", "blogPosts": [{"id": "/2024/05/07/0.9.9", "metadata": {"permalink": "/changelog/2024/05/07/0.9.9", "source": "@site/changelog/source/2024-05-07-0.9.9.md", "title": "0.9.9", "description": "New Features", "date": "2024-05-07T00:00:00.000Z", "tags": [{"inline": true, "label": "0.9.9", "permalink": "/changelog/tags/0-9-9"}, {"inline": true, "label": "changelog", "permalink": "/changelog/tags/changelog"}], "hasTruncateMarker": true, "authors": [], "frontMatter": {"date": "2024-05-07T00:00:00.000Z", "version": "0.9.9", "tags": ["0.9.9", "changelog"]}, "unlisted": false, "nextItem": {"title": "0.9.0", "permalink": "/changelog/2023/12/31/0.9.0"}, "listPageLink": "/changelog/"}, "content": "## :rocket: New Features\r\n  - **VirtualPiano Sheet Music Player** ([PRFP-463](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-463)) Added a basic virtual piano sheet music player! Find out more here: [VirtualPiano Sheet Music Player](/guides/midi-player/vp-sequencer/)\r\n  <img src=\"/img/guide/midi-player/461e41788b0be39c6df1407996ec3419.gif\" style={{marginLeft: '30px'}} alt=\"vp-sequencer\" width=\"500\"/>\r\n\r\n{/* truncate */}\r\n\r\n  - **Guests Can View Sheet Music Repo** Guest can now view the sheet music repository. They can't upload sheet music, but they can view the list of sheet music available. You can find it at `Tools -> Open Sheet Music Repo` or the `Sheet Music` button on the bottom bar.\r\n  <img src=\"/img/changelogs/7c8e3f01886679528e60312795b43ae7.gif\" style={{marginLeft: '30px'}} alt=\"sheet music repo\" width=\"500\"/>\r\n\r\n  - ([PRFP-1146](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1146)) Added more general analytics/stats about the app. You can find it on the bottom bar as `Leaderboards`.\r\n  <img src=\"/img/changelogs/81cf7c44ab8153a44091733bafa62c00.gif\" style={{marginLeft: '30px'}} alt=\"graph stats\" width=\"500\"/>\r\n\r\n  - ([PRFP-1221](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1221)) Added icons to represent the main sources of midi input by users: `PC Keyboard`, `Midi Piano`, and `Mouse`.\r\n  <img src=\"/img/changelogs/fda5e71948308019d98f4346ba11f732.gif\" style={{marginLeft: '30px'}} alt=\"note icons\" width=\"200\"/>\r\n\r\n  - ([PRFP-1219](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1219)) Added a cookie consent form. You can find it at the bottom of the page. This is to comply with the EU's GDPR regulations.\r\n  <img src=\"/img/changelogs/017e67675e7a434f0e307959ba89f007.png\" style={{marginLeft: '30px'}} alt=\"cookie consent form\" width=\"500\"/>\r\n\r\n  - ([PRFP-1068](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1068)) Added a search bar in many of the settings. Now it should be easier to find what you're looking for.\r\n  <img src=\"/img/changelogs/aa7b05f9a4989b1c6d0f04ffc0cdf83f.gif\" style={{marginLeft: '30px'}} alt=\"search bar\" width=\"500\"/>\r\n\r\n  - ([PRFP-1223](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1223)) Added support for an extended layout for VP. You can find it at `Settings -> Input -> MIDI to VP/QWERTY Layout`.\r\n  <img src=\"/img/changelogs/midi_to_qwerty_piano.png\" style={{marginLeft: '30px'}} alt=\"search bar\"/>\r\n\r\n  - ([PRFP-1097](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1097)) Added keyboard mapping overlays for 3D and 2D. You can toggle by pressing `F6` or going to the action widgets on the right and pressing `Show Input Mapping`.\r\n\r\n  - ([PRFP-1192](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1192)) Added a button to export logs for the desktop app. You can find it at `Settings -> Application -> Export Logs`.\r\n\r\n## :smile: Enhancements\r\n  - ([PRFP-936](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-936)) Reduced latency between keyboard input and sound output. The audio processing will use the main thread by default. The drawback is that it's more susceptible to instability spikes (may hear more static if there's too much load). You can toggle this in the settings by going to `Settings -> Soundfont -> Use AudioWorklet for audio processing`.\r\n  - ([PRFP-1202](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1202)) The client has gone through a major refactor. This should help with performance and stability.\r\n  - ([PRFP-1184](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1184)) Added a `Copy` button to the sheet music viewer.\r\n  - ([PRFP-1207](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1207)) Added a progress bar when downloading soundfonts.\r\n  - ([PRFP-1208](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1208)) Added a setting to toggle the audio equalizer.\r\n  - ([PRFP-1213](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1213)) Added a new soundfount: `SGM-v2.01-NicePianosGuitarsBass-V1.2`\r\n  - ([PRFP-1217](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1217)) Added a graphics setting for soft shadows.\r\n\r\n## :bug: Bug Fixes\r\n  - ([PRFP-1209](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1209)) Fixed issue with the 2D piano sometimes getting clipped on the edge of the window.\r\n  - ([PRFP-1203](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1203)) Fixed an issue with server banning.\r\n  - ([PRFP-1161](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1161)) Fixed the padding/spacing in the user profile \"About\" section text.\r\n  - ([PRFP-741](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-741)) Fixed an issue with youtube links with parameters not getting parsed correctly.\r\n  - ([PRFP-1204](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1204)) Fixed emails not being saved properly for new registrations.\r\n  - ([PRFP-1215](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1215)) Fixed an issue with urls not being parsed correctly in chat due to server censorship.\r\n\r\n<!----------------------------------------------->"}, {"id": "/2023/12/31/0.9.0", "metadata": {"permalink": "/changelog/2023/12/31/0.9.0", "source": "@site/changelog/source/2023-12-31-0.9.0.md", "title": "0.9.0", "description": "You can find out more about this update here in the blog: Server Upgrade", "date": "2023-12-31T00:00:00.000Z", "tags": [{"inline": true, "label": "0.9.0", "permalink": "/changelog/tags/0-9-0"}, {"inline": true, "label": "changelog", "permalink": "/changelog/tags/changelog"}], "hasTruncateMarker": true, "authors": [], "frontMatter": {"date": "2023-12-31T00:00:00.000Z", "version": "0.9.0", "tags": ["0.9.0", "changelog"]}, "unlisted": false, "prevItem": {"title": "0.9.9", "permalink": "/changelog/2024/05/07/0.9.9"}, "nextItem": {"title": "0.8.46", "permalink": "/changelog/2023/09/29/0.8.46"}, "listPageLink": "/changelog/"}, "content": "You can find out more about this update here in the blog: [Server Upgrade](/blog/update_2023/)\r\n\r\n{/* truncate */}\r\n\r\n## Versions\r\n  - Notes:\r\n    - If you're encountering any issues when creating a new room, try clicking the \"Reset\" button in the room creation settings. (Credit: @sun queen#61facd)\r\n\r\n  - `Version: 0.9.8` (2024-01-10)\r\n    - ## :rocket: New Features\r\n      - ([PRFP-1197](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1197)) Added two new lobbies: Forest and Studio! [Forest Stage](https://i.gyazo.com/ec010869e6adfb956342cb8d18f31f2a.gif)\r\n      - ([PRFP-1198](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1198)) Added volume slider for stage audio effects.\r\n    - ## :smile: Enhancements\r\n      - ([PRFP-1196](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1196)) Upgrade Tauri (Desktop client) to v1.5.4\r\n    - ## :bug: Bug Fixes\r\n      - ([PRFP-1199](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1199)) Fixed a potential issue with Discord login causing a failure to load on desktop.\r\n\r\n  - `Version: 0.9.7` (2024-01-07)\r\n    - ## :bug: Bug Fixes\r\n      - ([PRFP-1193](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1193)) Fixed issue with chat history deletion in normal rooms.\r\n      - ([PRFP-1195](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1195)) Fixed issue with clearing profile description not working.\r\n      - ([PRFP-1191](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1191)) Fixed issue with new Discord users not automatically registering a new account.\r\n      - ([PRFP-1190](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1190)) Fixed issue with input for `edit_badges` command.\r\n      - ([PRFP-1194](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1194)) Fixed `/clear_chat` command for mods.\r\n    - ## :smile: Enhancements\r\n      - ([PRFP-1186](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1186)) All active instruments are shown in the mini user profile.\r\n\r\n\r\n  - `Version: 0.9.6` (2024-01-07)\r\n    - Note: _You have may have to fully clear your cookies and relogin, if you're having issues logging in with this update._\r\n    - ## :bug: Bug Fixes\r\n      - ([PRFP-1188](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1188)) Fixed certain commands not using the full username.\r\n    - ## :smile: Enhancements\r\n      - ([PRFP-1187](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1187)) Added a new role: Trial Moderator.\r\n\r\n  - `Version: 0.9.5` (2024-01-06)\r\n    - ## :bug: Bug Fixes\r\n      - ([PRFP-1182](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1182)) Sheet music repo service should be back up now. Note: _You have may have to clear your cookies and relogin again if you get an error trying to access it._\r\n      - ([PRFP-1183](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1183)) (Server) Lobby chat messages should now persist after deployments.\r\n\r\n  - `Version: 0.9.3` (2024-01-02)\r\n    - Minor bug fixes for the front and backend.\r\n\r\n  - `Version: 0.9.1`\r\n    - ([PRFP-1179](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1179)) Sound fx not working on initial load.\r\n    - ([PRFP-1177](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1177)) Users can't join password protected rooms.\r\n\r\n\r\n<!----------------------------------------------->"}, {"id": "/2023/09/29/0.8.46", "metadata": {"permalink": "/changelog/2023/09/29/0.8.46", "source": "@site/changelog/source/2023-09-29-0.8.46.md", "title": "0.8.46", "description": "New Features", "date": "2023-09-29T00:00:00.000Z", "tags": [{"inline": true, "label": "0.8.46", "permalink": "/changelog/tags/0-8-46"}, {"inline": true, "label": "changelog", "permalink": "/changelog/tags/changelog"}], "hasTruncateMarker": true, "authors": [], "frontMatter": {"date": "2023-09-29T00:00:00.000Z", "version": "0.8.46", "tags": ["0.8.46", "changelog"]}, "unlisted": false, "prevItem": {"title": "0.9.0", "permalink": "/changelog/2023/12/31/0.9.0"}, "nextItem": {"title": "0.8.45", "permalink": "/changelog/2023/09/23/0.8.45"}, "listPageLink": "/changelog/"}, "content": "## :rocket: New Features\r\n  - **Added Self Hosting Rooms!** ([PRFP-1149](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1149)) Added the ability to host your own rooms! Find out more here: [Self Hosting Rooms](/tutorials/tutorial-self-host-rooms/)\r\n\r\n{/* truncate */}\r\n\r\n## :smile: Enhancements\r\n  - ([PRFP-1158](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1158)) Room creation settings are now saved locally so you don't have to keep redoing the inputs when creating a room. So this means that when you reload the page and you were the room owner, then it'll be created with the same settings (including password). _(note: the room password is also saved as plain text in the object in the local storage so I don't recommend using a password you using for other accounts)_\r\n  - ([PRFP-1166](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1166)) Gave good ol' @Near#80366e the `V2 OG MEMBER` badge that he's been waiting for, since forever. :)\r\n  - ([PRFP-1155](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1155)) Added a lobby just for PRO subscribers.\r\n  - To help with server bandwidth, if you're by yourself in a room, then no midi data will be emitted to the server (why didn't I think of this before?). Therefore, regardless of the room settings, note quota will not be a factor.\r\n\r\n## :bug: Bug Fixes\r\n  - ([PRFP-1169](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1169)) Fixed issue with logging in by email.\r\n  - ([PRFP-1159](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1159)) Fixed an issue with Orchestra mode rooms (such as the lobby) not showing other player pianos.\r\n  - ([PRFP-1157](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1157)) Fixed a UI issues where the lock icon was not immediately shown when muting a user.\r\n  - ([PRFP-1160](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1160)) Fixed user meta details (on the mini profile when you hover over a user) not immediately showing.\r\n  - ([PRFP-1167](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1167)) Fixed an issue where the app would get \"stuck\" when trying to enter a room with password in a new session. An \"enter room password\" modal should now show up.\r\n\r\n<!----------------------------------------------->"}, {"id": "/2023/09/23/0.8.45", "metadata": {"permalink": "/changelog/2023/09/23/0.8.45", "source": "@site/changelog/source/2023-09-23-0.8.45.md", "title": "0.8.45", "description": "No real enhancements mades to the client. Really just some minor things to accomodate some backend changes.", "date": "2023-09-23T00:00:00.000Z", "tags": [{"inline": true, "label": "0.8.45", "permalink": "/changelog/tags/0-8-45"}, {"inline": true, "label": "changelog", "permalink": "/changelog/tags/changelog"}], "hasTruncateMarker": false, "authors": [], "frontMatter": {"date": "2023-09-23T00:00:00.000Z", "version": "0.8.45", "tags": ["0.8.45", "changelog"]}, "unlisted": false, "prevItem": {"title": "0.8.46", "permalink": "/changelog/2023/09/29/0.8.46"}, "nextItem": {"title": "0.8.44", "permalink": "/changelog/2023/09/20/0.8.44"}, "listPageLink": "/changelog/"}, "content": "No real enhancements mades to the client. Really just some minor things to accomodate some backend changes.\r\n\r\n<!----------------------------------------------->"}, {"id": "/2023/09/20/0.8.44", "metadata": {"permalink": "/changelog/2023/09/20/0.8.44", "source": "@site/changelog/source/2023-09-20-0.8.44.md", "title": "0.8.44", "description": "Enhancements", "date": "2023-09-20T00:00:00.000Z", "tags": [{"inline": true, "label": "0.8.44", "permalink": "/changelog/tags/0-8-44"}, {"inline": true, "label": "changelog", "permalink": "/changelog/tags/changelog"}], "hasTruncateMarker": false, "authors": [], "frontMatter": {"date": "2023-09-20T00:00:00.000Z", "version": "0.8.44", "tags": ["0.8.44", "changelog"]}, "unlisted": false, "prevItem": {"title": "0.8.45", "permalink": "/changelog/2023/09/23/0.8.45"}, "nextItem": {"title": "0.8.43", "permalink": "/changelog/2023/09/12/0.8.43"}, "listPageLink": "/changelog/"}, "content": "## :smile: Enhancements\r\n  - Refactored the back end server by updating some dependencies and configurations. Should (hopefully) be less laggy now.\r\n\r\n## :bug: Bug Fixes\r\n  - ([PRFP-1117](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1117)) For PC keyboard players, fixed issue with shift not keeping held notes.\r\n  - ([PRFP-1132](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1132)) Fixed issue with account verification page not being found.\r\n  - ([PRFP-1130](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1130)) Fixed issue with the wrong username showing when a new user joins chat.\r\n\r\n## :soon: Coming soon\r\n  - ([PRFP-1137](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1137)) Fix whisper function.\r\n  - ([PRFP-1125](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1125)) Add Midi Repo.\r\n  - ([PRFP-1115](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1115)) Add \"Achievements.\"\r\n  - ([PRFP-1139](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1139)) Add the ability to record playing to midi tracks.\r\n  - ([PRFP-1138](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1138)) Add the ability to \"loop/auto-replay\" midi tracks\r\n\r\n<!----------------------------------------------->"}, {"id": "/2023/09/12/0.8.43", "metadata": {"permalink": "/changelog/2023/09/12/0.8.43", "source": "@site/changelog/source/2023-09-12-0.8.43.md", "title": "0.8.43", "description": "Enhancements", "date": "2023-09-12T00:00:00.000Z", "tags": [{"inline": true, "label": "0.8.43", "permalink": "/changelog/tags/0-8-43"}, {"inline": true, "label": "changelog", "permalink": "/changelog/tags/changelog"}], "hasTruncateMarker": false, "authors": [], "frontMatter": {"date": "2023-09-12T00:00:00.000Z", "version": "0.8.43", "tags": ["0.8.43", "changelog"]}, "unlisted": false, "prevItem": {"title": "0.8.44", "permalink": "/changelog/2023/09/20/0.8.44"}, "nextItem": {"title": "0.8.42", "permalink": "/changelog/2023/09/11/0.8.42"}, "listPageLink": "/changelog/"}, "content": "## :smile: Enhancements\r\n  - ([PRFP-1116](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1116)) Discord <PERSON><PERSON> _should_ show current players online in its status.\r\n  - Added more general stats (total notes & chat messages sent). You can see them in the leaderboards. Note: These are new metrics that wasn't previously being tracked, so everyone is starting from 0. Also, these particular metrics are only _updated after you logout_. So, they're not real time.\r\n\r\n## :bug: Bug Fixes\r\n  - Fixed an issue with users not being able to sign in with Discord.\r\n\r\n<!----------------------------------------------->"}, {"id": "/2023/09/11/0.8.42", "metadata": {"permalink": "/changelog/2023/09/11/0.8.42", "source": "@site/changelog/source/2023-09-11-0.8.42.md", "title": "0.8.42", "description": "Enhancements", "date": "2023-09-11T00:00:00.000Z", "tags": [{"inline": true, "label": "0.8.42", "permalink": "/changelog/tags/0-8-42"}, {"inline": true, "label": "changelog", "permalink": "/changelog/tags/changelog"}], "hasTruncateMarker": false, "authors": [], "frontMatter": {"date": "2023-09-11T00:00:00.000Z", "version": "0.8.42", "tags": ["0.8.42", "changelog"]}, "unlisted": false, "prevItem": {"title": "0.8.43", "permalink": "/changelog/2023/09/12/0.8.43"}, "nextItem": {"title": "0.8.41", "permalink": "/changelog/2023/09/10/0.8.41"}, "listPageLink": "/changelog/"}, "content": "## :smile: Enhancements\r\n  - ([PRFP-1111](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1111)) Show user mini profile when hovering over usernames in the leaderboard.\r\n  - ([PRFP-1109](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1109)) View your favorites list in the sheet music repo.\r\n  - ([PRFP-1108](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1108)) Added the ability to unbind single keybinds from the custom keyboard layout mapping.\r\n\r\n<!----------------------------------------------->"}, {"id": "/2023/09/10/0.8.41", "metadata": {"permalink": "/changelog/2023/09/10/0.8.41", "source": "@site/changelog/source/2023-09-10-0.8.41.md", "title": "0.8.41", "description": "Enhancements", "date": "2023-09-10T00:00:00.000Z", "tags": [{"inline": true, "label": "0.8.41", "permalink": "/changelog/tags/0-8-41"}, {"inline": true, "label": "changelog", "permalink": "/changelog/tags/changelog"}], "hasTruncateMarker": false, "authors": [], "frontMatter": {"date": "2023-09-10T00:00:00.000Z", "version": "0.8.41", "tags": ["0.8.41", "changelog"]}, "unlisted": false, "prevItem": {"title": "0.8.42", "permalink": "/changelog/2023/09/11/0.8.42"}, "nextItem": {"title": "0.8.40", "permalink": "/changelog/2023/09/09/0.8.40"}, "listPageLink": "/changelog/"}, "content": "## :smile: Enhancements\r\n  - ([PRFP-1106](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1106)) Added system status reports to client. PianoRhythm now has a [StatusPage](https://pianorhythm.statuspage.io/). You'll be able to find out the current state of the server.\r\n  - ([PRFP-515](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-515)) You can now \"favorite\" sheet music in the repository. With that said, a leaderboard of the most favorited sheet music has also been added.\r\n\r\n## :bug: Bug Fixes\r\n  - ([PRFP-1090](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1090)) Fixed issue midi sequencer not starting in web version due to suspended audio context.\r\n  - Fixed `Orchestra Mode` not showing up in the room modes when creating a new room.\r\n\r\n<!----------------------------------------------->"}, {"id": "/2023/09/09/0.8.40", "metadata": {"permalink": "/changelog/2023/09/09/0.8.40", "source": "@site/changelog/source/2023-09-09-0.8.40.md", "title": "0.8.40", "description": "New Features", "date": "2023-09-09T00:00:00.000Z", "tags": [{"inline": true, "label": "0.8.40", "permalink": "/changelog/tags/0-8-40"}, {"inline": true, "label": "changelog", "permalink": "/changelog/tags/changelog"}], "hasTruncateMarker": true, "authors": [], "frontMatter": {"date": "2023-09-09T00:00:00.000Z", "version": "0.8.40", "tags": ["0.8.40", "changelog"]}, "unlisted": false, "prevItem": {"title": "0.8.41", "permalink": "/changelog/2023/09/10/0.8.41"}, "nextItem": {"title": "0.8.39", "permalink": "/changelog/2023/09/05/0.8.39"}, "listPageLink": "/changelog/"}, "content": "## :rocket: New Features\r\n  - **Added <PERSON> Synth** ([PRFP-1101](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1101)) You can now change between different audio synthesizers. The default one has been called [OxiSynth](https://github.com/PolyMeilex/OxiSynth) and it's not perfect when it comes to how it handles certain soundfonts. [RustySynth](https://github.com/sinshu/rustysynth) seems to do it better. However, there are certain features (such as setting the max polyphony) that I haven't implemented within it, yet. <br/> To change the synthesizer, you can go to `Settings -> Soundfont -> Audio Synthesizer`.<br/>\r\n\r\n{/* truncate */}\r\n\r\n  - **Added Leaderboards** ([PRFP-1105](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1105)) Added leaderboards about general stats. More boards will be added over time. You can find it on the bottom bar as `Leaderboards`.\r\n\r\n## :smile: Enhancements\r\n  - ([PRFP-913](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-913)) Emails will not be parsed (as mailto:*) in chat.\r\n  - ([PRFP-1102](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1102)) Increased min/max supported transpose to -20/20, respectively.\r\n  - Updated the 3D graphics engine [Babylon.js](https://www.babylonjs.com/) to version 6.20.1.\r\n  - Some backend server refactoring.\r\n\r\n<!----------------------------------------------->"}, {"id": "/2023/09/05/0.8.39", "metadata": {"permalink": "/changelog/2023/09/05/0.8.39", "source": "@site/changelog/source/2023-09-05-0.8.39.md", "title": "0.8.39", "description": "New Features", "date": "2023-09-05T00:00:00.000Z", "tags": [{"inline": true, "label": "0.8.39", "permalink": "/changelog/tags/0-8-39"}, {"inline": true, "label": "changelog", "permalink": "/changelog/tags/changelog"}], "hasTruncateMarker": true, "authors": [], "frontMatter": {"date": "2023-09-05T00:00:00.000Z", "version": "0.8.39", "tags": ["0.8.39", "changelog"]}, "unlisted": false, "prevItem": {"title": "0.8.40", "permalink": "/changelog/2023/09/09/0.8.40"}, "nextItem": {"title": "0.8.30", "permalink": "/changelog/2023/07/17/0.8.30"}, "listPageLink": "/changelog/"}, "content": "## :rocket: New Features\r\n  - **Added 2D Mode** ([PRFP-1082](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1082)) After much contemplation, I've decided to add support back for a 2D renderer of the piano. You can toggle between 2D/3D (without having to restart) by clicking on the button that says `Camera Mode` in the button groups at the top right. _Note: Certain features such as the Midi Player are not available in 2d mode, until further notice._\r\n\r\n{/* truncate */}\r\n\r\n## :smile: Enhancements\r\n  - ([PRFP-1094](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1094)) Added room option for enabling/disabling bots in the room.\r\n  - Backend server refactoring.\r\n\r\n## :bug: Bug Fixes\r\n  - ([PRFP-1099](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1099)) Fixed issue with feedback loop with midi i/o.\r\n  - ([PRFP-1098](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1098)) Fixed volume icons not updating the user volume slider.\r\n  - Minor bug fixes.\r\n\r\n<!----------------------------------------------->"}, {"id": "/2023/07/17/0.8.30", "metadata": {"permalink": "/changelog/2023/07/17/0.8.30", "source": "@site/changelog/source/2023-07-17-0.8.30.md", "title": "0.8.30", "description": "New Features", "date": "2023-07-17T00:00:00.000Z", "tags": [{"inline": true, "label": "0.8.30", "permalink": "/changelog/tags/0-8-30"}, {"inline": true, "label": "changelog", "permalink": "/changelog/tags/changelog"}], "hasTruncateMarker": true, "authors": [], "frontMatter": {"date": "2023-07-17T00:00:00.000Z", "version": "0.8.30", "tags": ["0.8.30", "changelog"]}, "unlisted": false, "prevItem": {"title": "0.8.39", "permalink": "/changelog/2023/09/05/0.8.39"}, "nextItem": {"title": "0.8.26", "permalink": "/changelog/2023/06/17/0.8.26"}, "listPageLink": "/changelog/"}, "content": "## :rocket: New Features\r\n- `Version: 0.8.37`\r\n  - **Added a help chat bot!** ([PRFP-1089](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1089)) The first draft of the PianoRhythm Help Bot is now here. This was just a fun little thing to work on. You can ask it general questions about PianoRhythm. It's primary source is from the documentation site but you can also ask it some questions about the real time state of the server, such as how many players are online or the total number of registered users. To interact with it, just type in `@helpbot (insert question here)` in the chat bar.\r\n\r\n:::note\r\n  You must at least be a registered member to use the help bot. So create an account, today!\r\n:::\r\n\r\n{/* truncate */}\r\n\r\n- `Version: 0.8.34`\r\n  -  **Added build pipelines for MacOS and Linux** ([PRFP-1079](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1079)) Added desktop builds for MacOS and Linux. *Will need extensive testing*.\r\n  -  **Added customizable keyboard layout** ([PRFP-1080](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1080)) For those keyboard warriors out there, you can now\r\n  individually map each key on your keyboard to a corresponding note on the piano. You can find this at: `Settings -> Input -> (Piano Keys Layout) Custom`. Once selected, click on the `Customize Layout Keys` button.\r\n\r\n- `Version: 0.8.33`\r\n  - **Midi Step Sequencer** ([PRFP-502](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-502)) Added a midi/drum step sequencer. Find out more here: [Midi Step Sequencer](/guides/sequencer/)\r\n\r\n- `Version: 0.8.30`\r\n  - **Orchestra Mode!** ([PRFP-1026](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1026)) Added the first draft of orchestra mode. Find out more here: [Orchestra Mode](/guides/orchestra-mode/)\r\n  - **Piano Customization!** ([PRFP-1073](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1073)) Find out more here: [Piano Customization](/guides/customization/customization-piano)\r\n  - **Channel Parameter Sliders** ([PRFP-1070](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1070)) Added functionality to individually set the volume and pan values for channels. Find out more here: [Channel Parameters](/guides/instrument-dock/channel-parameters)\r\n  - **Increased Max Players Limit** ([PRFP-1072](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1072)) Increased max players to 20 for normal rooms and 30 for lobbies.\r\n  - **Added setting to set max channels for Multi Mode** ([PRFP-1074](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1074)) You can now set the max number of channels to use during multi mode. You can find this setting by going to: `Settings -> Midi -> Max Multi Mode Channels`\r\n\r\n## :smile: Enhancements\r\n- `Version: 0.8.34`\r\n  - ([PRFP-1084](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1084)) Increase default room size room to 20 in UI.\r\n  - ([PRFP-1081](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1081)) Add functionality for canvas to use main thread when offscreen canvas is not supported. You can also manually toggle this in the settings by going to: `Settings -> Graphics -> Enable Offscreen Canvas`\r\n- `Version: 0.8.30`\r\n  - ([PRFP-1071](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1071)) Volume button in the bottom bar is now highlighted when the global volume is muted or zero.\r\n  - ([PRFP-1066](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1066)) Added the user slot mode to the meta details in the mini profile card.\r\n\r\n## :bug: Bug Fixes\r\n- `Version: 0.8.38`\r\n  - Simply fixed an issue with the desktop builds not loading properly.\r\n- `Version: 0.8.35`\r\n  - ([PRFP-1075](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1075)) Fixed: Audio engine doesn't work when logging out and relogging back in, without refreshing page\r\n- `Version: 0.8.34`\r\n  - ([PRFP-1083](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1083)) Fixed an issue where notes from other users were not being emitted to midi outputs.\r\n  - ([PRFP-1085](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1085)) Fixed issue with MIDI player notes showing up behind the keys and going through the piano model.\r\n- `Version: 0.8.32`\r\n  - ([PRFP-1076](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1076)) PRFP-1076 Can't see other player notes playing on keys?\r\n  - ([PRFP-1077](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1077)) Instruments list not updating when switching soundfonts.\r\n  - ([PRFP-1078](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1078)) Keys with no sound not being represented properly.\r\n- `Version: 0.8.30`\r\n  - ([PRFP-1069](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1069)) Fixed an issue where turning on stage effects would remove the room's password.\r\n\r\n### General Notes\r\n- `Version: 0.8.30`\r\n  - Avatars (and the piano bench) have been disabled temporarily while I rework a more optimal system.\r\n  - Figured out how to use stereo audio (_it was apparently mono before_) for the web version.\r\n  - ([PRFP-1075](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1075)) There is a known issue about the audio engine not working when relogging in. Working on it!\r\n\r\n<!----------------------------------------------->"}, {"id": "/2023/06/17/0.8.26", "metadata": {"permalink": "/changelog/2023/06/17/0.8.26", "source": "@site/changelog/source/2023-06-17-0.8.26.md", "title": "0.8.26", "description": "Bug Fixes", "date": "2023-06-17T00:00:00.000Z", "tags": [{"inline": true, "label": "0.8.26", "permalink": "/changelog/tags/0-8-26"}, {"inline": true, "label": "changelog", "permalink": "/changelog/tags/changelog"}], "hasTruncateMarker": false, "authors": [], "frontMatter": {"date": "2023-06-17T00:00:00.000Z", "version": "0.8.26", "tags": ["0.8.26", "changelog"]}, "unlisted": false, "prevItem": {"title": "0.8.30", "permalink": "/changelog/2023/07/17/0.8.30"}, "nextItem": {"title": "0.8.24", "permalink": "/changelog/2023/05/27/0.8.24"}, "listPageLink": "/changelog/"}, "content": "## :bug: Bug Fixes\r\n- ([PRFP-1067](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1067)) Fixed an issue with crashes when playing certain midi notes/events.\r\n- ([PRFP-1065](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1065)) Fixed the pageloader screen showing indefinitely.\r\n\r\n### General Notes\r\n- You may have noticed that there hasn't been a lot of updates lately. I've just been taking a general\r\n  break from v3 and programming. But I've been slowly getting back into the groove of things.\r\n  Thank you guys for all the support you've given so far. ❤\r\n- Some things I'm currently going to or have been working on:\r\n  - Ensuring that the reverb effect works across all soundfonts.\r\n  - Volume scaling sliders for mixing instrument channels in MultiChannel mode.\r\n  - Add a feature to loop/auto replay midi tracks.\r\n  - Add some basic drum loops for when you're in 'Play Drums' mode.\r\n  - Implementing a *Orchestra* like room/mode. You'll be able to customize your piano and also see everyone else's.\r\n  - Increase max users count in rooms by double.\r\n  - Improve logging and to better deal with crashes that lead to audio/sound issues.\r\n\r\n<!----------------------------------------------->"}, {"id": "/2023/05/27/0.8.24", "metadata": {"permalink": "/changelog/2023/05/27/0.8.24", "source": "@site/changelog/source/2023-05-27-0.8.24.md", "title": "0.8.24", "description": "Changes", "date": "2023-05-27T00:00:00.000Z", "tags": [{"inline": true, "label": "0.8.24", "permalink": "/changelog/tags/0-8-24"}, {"inline": true, "label": "changelog", "permalink": "/changelog/tags/changelog"}], "hasTruncateMarker": false, "authors": [], "frontMatter": {"date": "2023-05-27T00:00:00.000Z", "version": "0.8.24", "tags": ["0.8.24", "changelog"]}, "unlisted": false, "prevItem": {"title": "0.8.26", "permalink": "/changelog/2023/06/17/0.8.26"}, "nextItem": {"title": "0.8.0", "permalink": "/changelog/2023/05/04/0.8.0"}, "listPageLink": "/changelog/"}, "content": "## :ok_hand: Changes\r\n- ([PRFP-1062](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1062)) Created a Telegram group! Join here: https://t.me/+hUJtV_QXVnU1NTIx\r\n- ([PRFP-1059](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1059)) Added \"Arco Strings\" soundfont to default list.\r\n- ([PRFP-1063](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1063)) Updated the default reverb settings.\r\n- ([PRFP-1020](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1020)) Added a reset functionality for soundfont settings.\r\n\r\n## :bug: Bug Fixes\r\n- ([PRFP-1061](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1061)) Fixed a note-off issue for instruments in multi-channel mode.\r\n- ([PRFP-1060](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1060)) Fixed an issue with users being able to add instruments to disabled channels.\r\n\r\n<!----------------------------------------------->"}, {"id": "/2023/05/04/0.8.0", "metadata": {"permalink": "/changelog/2023/05/04/0.8.0", "source": "@site/changelog/source/2023-05-04-0.8.0.md", "title": "0.8.0", "description": "New Features", "date": "2023-05-04T00:00:00.000Z", "tags": [{"inline": true, "label": "0.8.0", "permalink": "/changelog/tags/0-8-0"}, {"inline": true, "label": "changelog", "permalink": "/changelog/tags/changelog"}], "hasTruncateMarker": true, "authors": [], "frontMatter": {"date": "2023-05-04T00:00:00.000Z", "version": "0.8.0", "tags": ["0.8.0", "changelog"]}, "unlisted": false, "prevItem": {"title": "0.8.24", "permalink": "/changelog/2023/05/27/0.8.24"}, "nextItem": {"title": "0.7.230", "permalink": "/changelog/2023/03/18/0.7.230"}, "listPageLink": "/changelog/"}, "content": "## :rocket: New Features\r\n\r\n- **Added plugins!** ([PRFP-629](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-629)) Added the first draft of a plugin system to allow users\r\nto build local plugins to extend PianoRhythm. Find out more here: [Plugins Guide](/advanced-guides/plugins)\r\n\r\n{/* truncate */}\r\n\r\n- **Added Changelog Modal** ([PRFP-1022](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1022)) A modal of the latest changes will now show up after a new version update.\r\n\r\n- **Added User Velocity Percentage Slider** ([PRFP-1045](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1045)) Added a global slider alongside individual sliders for controlling the total percentage value of other users' velocity inputs. You can find the global setting in `Settings -> Midi` and find individual sliders by clicking on user profiles in the sidebar.\r\n\r\n## :ok_hand: Changes\r\n- **Updated Desktop App (Tauri -> v1.3.0)** If you would like to know the technical details, you\r\ncan check out their blog post: [Tauri 1.3.0](https://tauri.app/blog/2023/05/03/tauri-1-3)\r\n- **Updated Graphics Engine (Bablyon.js -> v6.0)** ([PRFP-1024](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1024)) Find out more here [Babylon.js v6.0](https://babylonjs.medium.com/announcing-babylon-js-6-0-dcb5f1662e3a)\r\n- ([PRFP-1035](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1035)) (Development) Updated Vite to 4.\r\n- ([PRFP-1033](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1033)) (Development) Added some more much needed unit tests.\r\n- ([PRFP-1033](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1033)) (Development) Added some much needed E2E (Cypress) tests.\r\n- ([PRFP-1055](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1055)) Added an option to experiment with WebGPU rendering.\r\n- ([PRFP-1043](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1043)) Added a visual indication when a user has a different soundfont loaded than you do.\r\n- (Development) Minor internal refactors.\r\n\r\n## :bug: Bug Fixes\r\n- ([PRFP-1021](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1021)) Fixed an issue with other players not being able to hear your drums.\r\n- ([PRFP-1029](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1029)) Fixed a glitch with the room owner's crown display.\r\n- ([PRFP-1034](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1034)) Fixed issue with executing unit tests.\r\n- ([PRFP-1030](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1030)) Fixed issue with the reverb modal and other related modals, due to the UI sliders (noUiSlider) being broken.\r\n- ([PRFP-1032](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1032)) Fixed glitch with the `New Messages` button sometimes showing up inappropriately.\r\n- ([PRFP-1036](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1036)) Fixed an issue with trying to display the latest changelog from the docs page.\r\n- ([PRFP-1039](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-1039)) Fixed multi channel slot mode not emitting properly in the desktop app.\r\n- Minor internal bug fixes.\r\n\r\n<!----------------------------------------------->"}, {"id": "/2023/03/18/0.7.230", "metadata": {"permalink": "/changelog/2023/03/18/0.7.230", "source": "@site/changelog/source/2023-03-18-0.7.230.md", "title": "0.7.230", "description": "New Features", "date": "2023-03-18T00:00:00.000Z", "tags": [{"inline": true, "label": "0.7.230", "permalink": "/changelog/tags/0-7-230"}, {"inline": true, "label": "changelog", "permalink": "/changelog/tags/changelog"}], "hasTruncateMarker": false, "authors": [], "frontMatter": {"date": "2023-03-18T00:00:00.000Z", "version": "0.7.230", "tags": ["0.7.230", "changelog"]}, "unlisted": false, "prevItem": {"title": "0.8.0", "permalink": "/changelog/2023/05/04/0.8.0"}, "nextItem": {"title": "0.7.222", "permalink": "/changelog/2023/03/13/0.7.222"}, "listPageLink": "/changelog/"}, "content": "## :rocket: New Features\r\n\r\n- **Added sound effects** ([PRFP-137](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-137)) Implemented the first draft of having sound effects with UI Elements! You can find more settings at `Settings -> Audio Effects`\r\n\r\n<!----------------------------------------------->"}, {"id": "/2023/03/13/0.7.222", "metadata": {"permalink": "/changelog/2023/03/13/0.7.222", "source": "@site/changelog/source/2023-03-13-0.7.222.md", "title": "0.7.222", "description": "New Features", "date": "2023-03-13T00:00:00.000Z", "tags": [{"inline": true, "label": "0.7.222", "permalink": "/changelog/tags/0-7-222"}, {"inline": true, "label": "changelog", "permalink": "/changelog/tags/changelog"}], "hasTruncateMarker": false, "authors": [], "frontMatter": {"date": "2023-03-13T00:00:00.000Z", "version": "0.7.222", "tags": ["0.7.222", "changelog"]}, "unlisted": false, "prevItem": {"title": "0.7.230", "permalink": "/changelog/2023/03/18/0.7.230"}, "nextItem": {"title": "0.7.152", "permalink": "/changelog/2023/01/09/0.7.152"}, "listPageLink": "/changelog/"}, "content": "## :rocket: New Features\r\n\r\n- **Added initial draft of World** PianoRhythm World is a feature that I've always wanted to implement. It's an environment where you can create your own avatars and engage in a 3D world with others while playing music.\r\n\r\n- **Added support for displaying images in chat** ([PRFP-956](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-956)) For certain links that contain images, there'll be a preview displayed in chat\r\n\r\n- **Added Reporting** ([PRFP-225](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-225)) Still in the initial stages but you can now report certain chat messages and users for inappropriate content/behaviours. You can right click a chat message or user to see the context of being able to report.\r\n\r\n<!----------------------------------------------->"}, {"id": "/2023/01/09/0.7.152", "metadata": {"permalink": "/changelog/2023/01/09/0.7.152", "source": "@site/changelog/source/2023-01-09-0.7.152.md", "title": "0.7.152", "description": "New Features", "date": "2023-01-09T00:00:00.000Z", "tags": [{"inline": true, "label": "0.7.152", "permalink": "/changelog/tags/0-7-152"}, {"inline": true, "label": "changelog", "permalink": "/changelog/tags/changelog"}], "hasTruncateMarker": false, "authors": [], "frontMatter": {"date": "2023-01-09T00:00:00.000Z", "version": "0.7.152", "tags": ["0.7.152", "changelog"]}, "unlisted": false, "prevItem": {"title": "0.7.222", "permalink": "/changelog/2023/03/13/0.7.222"}, "nextItem": {"title": "0.7.143", "permalink": "/changelog/2022/12/26/0.7.143"}, "listPageLink": "/changelog/"}, "content": "## :rocket: New Features\r\n\r\n- **Added Audio Equalizer** ([PRFP-918](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-918)) Added an audio equalizer to allow for better fine tuning of the audio output. You can find it `Tools -> Audio Equalizer`\r\n\r\n<!----------------------------------------------->"}, {"id": "/2022/12/26/0.7.143", "metadata": {"permalink": "/changelog/2022/12/26/0.7.143", "source": "@site/changelog/source/2022-12-26-0.7.143.md", "title": "0.7.143", "description": "New Features", "date": "2022-12-26T00:00:00.000Z", "tags": [{"inline": true, "label": "0.7.143", "permalink": "/changelog/tags/0-7-143"}, {"inline": true, "label": "changelog", "permalink": "/changelog/tags/changelog"}], "hasTruncateMarker": false, "authors": [], "frontMatter": {"date": "2022-12-26T00:00:00.000Z", "version": "0.7.143", "tags": ["0.7.143", "changelog"]}, "unlisted": false, "prevItem": {"title": "0.7.152", "permalink": "/changelog/2023/01/09/0.7.152"}, "nextItem": {"title": "0.7.146", "permalink": "/changelog/2022/12/26/0.7.146"}, "listPageLink": "/changelog/"}, "content": "## :rocket: New Features\r\n- **Improved Soundfont Support** If you previously had certain custom soundfonts that would not load, try loading them again. I updated to code to be a bit more friendlier. There are still going to be some exceptions.\r\n\r\n<!----------------------------------------------->"}, {"id": "/2022/12/26/0.7.146", "metadata": {"permalink": "/changelog/2022/12/26/0.7.146", "source": "@site/changelog/source/2022-12-26-0.7.146.md", "title": "0.7.146", "description": "New Features", "date": "2022-12-26T00:00:00.000Z", "tags": [{"inline": true, "label": "0.7.146", "permalink": "/changelog/tags/0-7-146"}, {"inline": true, "label": "changelog", "permalink": "/changelog/tags/changelog"}], "hasTruncateMarker": false, "authors": [], "frontMatter": {"date": "2022-12-26T00:00:00.000Z", "version": "0.7.146", "tags": ["0.7.146", "changelog"]}, "unlisted": false, "prevItem": {"title": "0.7.143", "permalink": "/changelog/2022/12/26/0.7.143"}, "nextItem": {"title": "0.7.123", "permalink": "/changelog/2022/12/20/0.7.123"}, "listPageLink": "/changelog/"}, "content": "## :rocket: New Features\r\n- **Added V2 Piano Soundfont** ([PRFP-900](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-900)) I did my best to convert the default piano from v2 into a soundfont. For now, I set it as the default soundfont. You can go to `Settings -> Soundfont -> <PERSON><PERSON> Default Soundfont.`\r\n\r\n  In the list, there's the `PR_V2_SF.sf2` which is just the piano itself and `PR_GM2.sf2` which has the v2 piano mixed with the previous GM2_Map sf.\r\n\r\n<!----------------------------------------------->"}, {"id": "/2022/12/20/0.7.123", "metadata": {"permalink": "/changelog/2022/12/20/0.7.123", "source": "@site/changelog/source/2022-12-20-0.7.123.md", "title": "0.7.123", "description": "New Features", "date": "2022-12-20T00:00:00.000Z", "tags": [{"inline": true, "label": "0.7.123", "permalink": "/changelog/tags/0-7-123"}, {"inline": true, "label": "changelog", "permalink": "/changelog/tags/changelog"}], "hasTruncateMarker": false, "authors": [], "frontMatter": {"date": "2022-12-20T00:00:00.000Z", "version": "0.7.123", "tags": ["0.7.123", "changelog"]}, "unlisted": false, "prevItem": {"title": "0.7.146", "permalink": "/changelog/2022/12/26/0.7.146"}, "nextItem": {"title": "0.7.119", "permalink": "/changelog/2022/12/18/0.7.119"}, "listPageLink": "/changelog/"}, "content": "## :rocket: New Features\r\n\r\n- **Sheet Music Repo** ([PRFP-851](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-851)) Added a repository for uploading sheet music! To check it out, click on `Tools -> Open Sheet Music Repo`. This feature is not available for guests, so sign up and create an account today!\r\n\r\n<!----------------------------------------------->"}, {"id": "/2022/12/18/0.7.119", "metadata": {"permalink": "/changelog/2022/12/18/0.7.119", "source": "@site/changelog/source/2022-12-18-0.7.119.md", "title": "0.7.119", "description": "New Features", "date": "2022-12-18T00:00:00.000Z", "tags": [{"inline": true, "label": "0.7.119", "permalink": "/changelog/tags/0-7-119"}, {"inline": true, "label": "changelog", "permalink": "/changelog/tags/changelog"}], "hasTruncateMarker": false, "authors": [], "frontMatter": {"date": "2022-12-18T00:00:00.000Z", "version": "0.7.119", "tags": ["0.7.119", "changelog"]}, "unlisted": false, "prevItem": {"title": "0.7.123", "permalink": "/changelog/2022/12/20/0.7.123"}, "nextItem": {"title": "0.7.91", "permalink": "/changelog/2022/12/07/0.7.91"}, "listPageLink": "/changelog/page/2"}, "content": "## :rocket: New Features\r\n\r\n- **Graphics Optimization** Optimized the default scene so some you guys should experience a decent boost to frame rate. Also add more graphical settings that you can mess with to see what improves your framerate. You can check it out at `Settings > General (Graphics)`.\r\n\r\n<!----------------------------------------------->"}, {"id": "/2022/12/07/0.7.91", "metadata": {"permalink": "/changelog/2022/12/07/0.7.91", "source": "@site/changelog/source/2022-12-07-0.7.91.md", "title": "0.7.91", "description": "New Features", "date": "2022-12-07T00:00:00.000Z", "tags": [{"inline": true, "label": "0.7.91", "permalink": "/changelog/tags/0-7-91"}, {"inline": true, "label": "changelog", "permalink": "/changelog/tags/changelog"}], "hasTruncateMarker": false, "authors": [], "frontMatter": {"date": "2022-12-07T00:00:00.000Z", "version": "0.7.91", "tags": ["0.7.91", "changelog"]}, "unlisted": false, "prevItem": {"title": "0.7.119", "permalink": "/changelog/2022/12/18/0.7.119"}, "listPageLink": "/changelog/page/2"}, "content": "## :rocket: New Features\r\n- **UI Themes** Added themes! This is just the first draft of this feature so there are certain color issues on some themes. They'll be eventually fixed in the coming updates. To change a theme, go to `Settings -> (Graphics) UI -> Theme`\r\n\r\n- **Reverb Settings** ([PRFP-988](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-988)) Added customizable reverb settings. To adjust them, go to `Settings -> Soundfont -> Advanced`\r\n\r\n- **New stage models!** You can try the new stages by going to `New Room -> Room Stage`.\r\n\r\n- **Ear Training Games** These exercises will improve your musical ability by developing a more intuitive understanding of what you hear. Two exercises were added: `Perfect Pitch` and `Scales`. To try them out, go to `New Room -> Room Mode -> SoloGame`.\r\n\r\n- **Midi Player** Added a built-in midi player that renders like <PERSON><PERSON><PERSON><PERSON>'s waterfall notes. You can test it out by dragging a midi file in the main window or by going to `Instrument Dock -> Tools -> Open Midi File`.\r\n\r\n## :bug: Bug Fixes\r\n- ([PRFP-807](https://pianorhythm.myjetbrains.com/youtrack/issue/PRFP-807)) Fixed issue with Touhou soundfont only emitting sound to one speaker from the lower notes."}], "blogListPaginated": [{"items": ["/2024/05/07/0.9.9", "/2023/12/31/0.9.0", "/2023/09/29/0.8.46", "/2023/09/23/0.8.45", "/2023/09/20/0.8.44", "/2023/09/12/0.8.43", "/2023/09/11/0.8.42", "/2023/09/10/0.8.41", "/2023/09/09/0.8.40", "/2023/09/05/0.8.39", "/2023/07/17/0.8.30", "/2023/06/17/0.8.26", "/2023/05/27/0.8.24", "/2023/05/04/0.8.0", "/2023/03/18/0.7.230", "/2023/03/13/0.7.222", "/2023/01/09/0.7.152", "/2022/12/26/0.7.143", "/2022/12/26/0.7.146", "/2022/12/20/0.7.123"], "metadata": {"permalink": "/changelog", "page": 1, "postsPerPage": 20, "totalPages": 2, "totalCount": 22, "nextPage": "/changelog/page/2", "blogDescription": "Keep yourself up-to-date about new features in every release", "blogTitle": "PianoRhythm changelog"}}, {"items": ["/2022/12/18/0.7.119", "/2022/12/07/0.7.91"], "metadata": {"permalink": "/changelog/page/2", "page": 2, "postsPerPage": 20, "totalPages": 2, "totalCount": 22, "previousPage": "/changelog", "blogDescription": "Keep yourself up-to-date about new features in every release", "blogTitle": "PianoRhythm changelog"}}], "blogTags": {"/changelog/tags/0-9-9": {"inline": true, "label": "0.9.9", "permalink": "/changelog/tags/0-9-9", "items": ["/2024/05/07/0.9.9"], "pages": [{"items": ["/2024/05/07/0.9.9"], "metadata": {"permalink": "/changelog/tags/0-9-9", "page": 1, "postsPerPage": 20, "totalPages": 1, "totalCount": 1, "blogDescription": "Keep yourself up-to-date about new features in every release", "blogTitle": "PianoRhythm changelog"}}], "unlisted": false}, "/changelog/tags/changelog": {"inline": true, "label": "changelog", "permalink": "/changelog/tags/changelog", "items": ["/2024/05/07/0.9.9", "/2023/12/31/0.9.0", "/2023/09/29/0.8.46", "/2023/09/23/0.8.45", "/2023/09/20/0.8.44", "/2023/09/12/0.8.43", "/2023/09/11/0.8.42", "/2023/09/10/0.8.41", "/2023/09/09/0.8.40", "/2023/09/05/0.8.39", "/2023/07/17/0.8.30", "/2023/06/17/0.8.26", "/2023/05/27/0.8.24", "/2023/05/04/0.8.0", "/2023/03/18/0.7.230", "/2023/03/13/0.7.222", "/2023/01/09/0.7.152", "/2022/12/26/0.7.143", "/2022/12/26/0.7.146", "/2022/12/20/0.7.123", "/2022/12/18/0.7.119", "/2022/12/07/0.7.91"], "pages": [{"items": ["/2024/05/07/0.9.9", "/2023/12/31/0.9.0", "/2023/09/29/0.8.46", "/2023/09/23/0.8.45", "/2023/09/20/0.8.44", "/2023/09/12/0.8.43", "/2023/09/11/0.8.42", "/2023/09/10/0.8.41", "/2023/09/09/0.8.40", "/2023/09/05/0.8.39", "/2023/07/17/0.8.30", "/2023/06/17/0.8.26", "/2023/05/27/0.8.24", "/2023/05/04/0.8.0", "/2023/03/18/0.7.230", "/2023/03/13/0.7.222", "/2023/01/09/0.7.152", "/2022/12/26/0.7.143", "/2022/12/26/0.7.146", "/2022/12/20/0.7.123"], "metadata": {"permalink": "/changelog/tags/changelog", "page": 1, "postsPerPage": 20, "totalPages": 2, "totalCount": 22, "nextPage": "/changelog/tags/changelog/page/2", "blogDescription": "Keep yourself up-to-date about new features in every release", "blogTitle": "PianoRhythm changelog"}}, {"items": ["/2022/12/18/0.7.119", "/2022/12/07/0.7.91"], "metadata": {"permalink": "/changelog/tags/changelog/page/2", "page": 2, "postsPerPage": 20, "totalPages": 2, "totalCount": 22, "previousPage": "/changelog/tags/changelog", "blogDescription": "Keep yourself up-to-date about new features in every release", "blogTitle": "PianoRhythm changelog"}}], "unlisted": false}, "/changelog/tags/0-9-0": {"inline": true, "label": "0.9.0", "permalink": "/changelog/tags/0-9-0", "items": ["/2023/12/31/0.9.0"], "pages": [{"items": ["/2023/12/31/0.9.0"], "metadata": {"permalink": "/changelog/tags/0-9-0", "page": 1, "postsPerPage": 20, "totalPages": 1, "totalCount": 1, "blogDescription": "Keep yourself up-to-date about new features in every release", "blogTitle": "PianoRhythm changelog"}}], "unlisted": false}, "/changelog/tags/0-8-46": {"inline": true, "label": "0.8.46", "permalink": "/changelog/tags/0-8-46", "items": ["/2023/09/29/0.8.46"], "pages": [{"items": ["/2023/09/29/0.8.46"], "metadata": {"permalink": "/changelog/tags/0-8-46", "page": 1, "postsPerPage": 20, "totalPages": 1, "totalCount": 1, "blogDescription": "Keep yourself up-to-date about new features in every release", "blogTitle": "PianoRhythm changelog"}}], "unlisted": false}, "/changelog/tags/0-8-45": {"inline": true, "label": "0.8.45", "permalink": "/changelog/tags/0-8-45", "items": ["/2023/09/23/0.8.45"], "pages": [{"items": ["/2023/09/23/0.8.45"], "metadata": {"permalink": "/changelog/tags/0-8-45", "page": 1, "postsPerPage": 20, "totalPages": 1, "totalCount": 1, "blogDescription": "Keep yourself up-to-date about new features in every release", "blogTitle": "PianoRhythm changelog"}}], "unlisted": false}, "/changelog/tags/0-8-44": {"inline": true, "label": "0.8.44", "permalink": "/changelog/tags/0-8-44", "items": ["/2023/09/20/0.8.44"], "pages": [{"items": ["/2023/09/20/0.8.44"], "metadata": {"permalink": "/changelog/tags/0-8-44", "page": 1, "postsPerPage": 20, "totalPages": 1, "totalCount": 1, "blogDescription": "Keep yourself up-to-date about new features in every release", "blogTitle": "PianoRhythm changelog"}}], "unlisted": false}, "/changelog/tags/0-8-43": {"inline": true, "label": "0.8.43", "permalink": "/changelog/tags/0-8-43", "items": ["/2023/09/12/0.8.43"], "pages": [{"items": ["/2023/09/12/0.8.43"], "metadata": {"permalink": "/changelog/tags/0-8-43", "page": 1, "postsPerPage": 20, "totalPages": 1, "totalCount": 1, "blogDescription": "Keep yourself up-to-date about new features in every release", "blogTitle": "PianoRhythm changelog"}}], "unlisted": false}, "/changelog/tags/0-8-42": {"inline": true, "label": "0.8.42", "permalink": "/changelog/tags/0-8-42", "items": ["/2023/09/11/0.8.42"], "pages": [{"items": ["/2023/09/11/0.8.42"], "metadata": {"permalink": "/changelog/tags/0-8-42", "page": 1, "postsPerPage": 20, "totalPages": 1, "totalCount": 1, "blogDescription": "Keep yourself up-to-date about new features in every release", "blogTitle": "PianoRhythm changelog"}}], "unlisted": false}, "/changelog/tags/0-8-41": {"inline": true, "label": "0.8.41", "permalink": "/changelog/tags/0-8-41", "items": ["/2023/09/10/0.8.41"], "pages": [{"items": ["/2023/09/10/0.8.41"], "metadata": {"permalink": "/changelog/tags/0-8-41", "page": 1, "postsPerPage": 20, "totalPages": 1, "totalCount": 1, "blogDescription": "Keep yourself up-to-date about new features in every release", "blogTitle": "PianoRhythm changelog"}}], "unlisted": false}, "/changelog/tags/0-8-40": {"inline": true, "label": "0.8.40", "permalink": "/changelog/tags/0-8-40", "items": ["/2023/09/09/0.8.40"], "pages": [{"items": ["/2023/09/09/0.8.40"], "metadata": {"permalink": "/changelog/tags/0-8-40", "page": 1, "postsPerPage": 20, "totalPages": 1, "totalCount": 1, "blogDescription": "Keep yourself up-to-date about new features in every release", "blogTitle": "PianoRhythm changelog"}}], "unlisted": false}, "/changelog/tags/0-8-39": {"inline": true, "label": "0.8.39", "permalink": "/changelog/tags/0-8-39", "items": ["/2023/09/05/0.8.39"], "pages": [{"items": ["/2023/09/05/0.8.39"], "metadata": {"permalink": "/changelog/tags/0-8-39", "page": 1, "postsPerPage": 20, "totalPages": 1, "totalCount": 1, "blogDescription": "Keep yourself up-to-date about new features in every release", "blogTitle": "PianoRhythm changelog"}}], "unlisted": false}, "/changelog/tags/0-8-30": {"inline": true, "label": "0.8.30", "permalink": "/changelog/tags/0-8-30", "items": ["/2023/07/17/0.8.30"], "pages": [{"items": ["/2023/07/17/0.8.30"], "metadata": {"permalink": "/changelog/tags/0-8-30", "page": 1, "postsPerPage": 20, "totalPages": 1, "totalCount": 1, "blogDescription": "Keep yourself up-to-date about new features in every release", "blogTitle": "PianoRhythm changelog"}}], "unlisted": false}, "/changelog/tags/0-8-26": {"inline": true, "label": "0.8.26", "permalink": "/changelog/tags/0-8-26", "items": ["/2023/06/17/0.8.26"], "pages": [{"items": ["/2023/06/17/0.8.26"], "metadata": {"permalink": "/changelog/tags/0-8-26", "page": 1, "postsPerPage": 20, "totalPages": 1, "totalCount": 1, "blogDescription": "Keep yourself up-to-date about new features in every release", "blogTitle": "PianoRhythm changelog"}}], "unlisted": false}, "/changelog/tags/0-8-24": {"inline": true, "label": "0.8.24", "permalink": "/changelog/tags/0-8-24", "items": ["/2023/05/27/0.8.24"], "pages": [{"items": ["/2023/05/27/0.8.24"], "metadata": {"permalink": "/changelog/tags/0-8-24", "page": 1, "postsPerPage": 20, "totalPages": 1, "totalCount": 1, "blogDescription": "Keep yourself up-to-date about new features in every release", "blogTitle": "PianoRhythm changelog"}}], "unlisted": false}, "/changelog/tags/0-8-0": {"inline": true, "label": "0.8.0", "permalink": "/changelog/tags/0-8-0", "items": ["/2023/05/04/0.8.0"], "pages": [{"items": ["/2023/05/04/0.8.0"], "metadata": {"permalink": "/changelog/tags/0-8-0", "page": 1, "postsPerPage": 20, "totalPages": 1, "totalCount": 1, "blogDescription": "Keep yourself up-to-date about new features in every release", "blogTitle": "PianoRhythm changelog"}}], "unlisted": false}, "/changelog/tags/0-7-230": {"inline": true, "label": "0.7.230", "permalink": "/changelog/tags/0-7-230", "items": ["/2023/03/18/0.7.230"], "pages": [{"items": ["/2023/03/18/0.7.230"], "metadata": {"permalink": "/changelog/tags/0-7-230", "page": 1, "postsPerPage": 20, "totalPages": 1, "totalCount": 1, "blogDescription": "Keep yourself up-to-date about new features in every release", "blogTitle": "PianoRhythm changelog"}}], "unlisted": false}, "/changelog/tags/0-7-222": {"inline": true, "label": "0.7.222", "permalink": "/changelog/tags/0-7-222", "items": ["/2023/03/13/0.7.222"], "pages": [{"items": ["/2023/03/13/0.7.222"], "metadata": {"permalink": "/changelog/tags/0-7-222", "page": 1, "postsPerPage": 20, "totalPages": 1, "totalCount": 1, "blogDescription": "Keep yourself up-to-date about new features in every release", "blogTitle": "PianoRhythm changelog"}}], "unlisted": false}, "/changelog/tags/0-7-152": {"inline": true, "label": "0.7.152", "permalink": "/changelog/tags/0-7-152", "items": ["/2023/01/09/0.7.152"], "pages": [{"items": ["/2023/01/09/0.7.152"], "metadata": {"permalink": "/changelog/tags/0-7-152", "page": 1, "postsPerPage": 20, "totalPages": 1, "totalCount": 1, "blogDescription": "Keep yourself up-to-date about new features in every release", "blogTitle": "PianoRhythm changelog"}}], "unlisted": false}, "/changelog/tags/0-7-143": {"inline": true, "label": "0.7.143", "permalink": "/changelog/tags/0-7-143", "items": ["/2022/12/26/0.7.143"], "pages": [{"items": ["/2022/12/26/0.7.143"], "metadata": {"permalink": "/changelog/tags/0-7-143", "page": 1, "postsPerPage": 20, "totalPages": 1, "totalCount": 1, "blogDescription": "Keep yourself up-to-date about new features in every release", "blogTitle": "PianoRhythm changelog"}}], "unlisted": false}, "/changelog/tags/0-7-146": {"inline": true, "label": "0.7.146", "permalink": "/changelog/tags/0-7-146", "items": ["/2022/12/26/0.7.146"], "pages": [{"items": ["/2022/12/26/0.7.146"], "metadata": {"permalink": "/changelog/tags/0-7-146", "page": 1, "postsPerPage": 20, "totalPages": 1, "totalCount": 1, "blogDescription": "Keep yourself up-to-date about new features in every release", "blogTitle": "PianoRhythm changelog"}}], "unlisted": false}, "/changelog/tags/0-7-123": {"inline": true, "label": "0.7.123", "permalink": "/changelog/tags/0-7-123", "items": ["/2022/12/20/0.7.123"], "pages": [{"items": ["/2022/12/20/0.7.123"], "metadata": {"permalink": "/changelog/tags/0-7-123", "page": 1, "postsPerPage": 20, "totalPages": 1, "totalCount": 1, "blogDescription": "Keep yourself up-to-date about new features in every release", "blogTitle": "PianoRhythm changelog"}}], "unlisted": false}, "/changelog/tags/0-7-119": {"inline": true, "label": "0.7.119", "permalink": "/changelog/tags/0-7-119", "items": ["/2022/12/18/0.7.119"], "pages": [{"items": ["/2022/12/18/0.7.119"], "metadata": {"permalink": "/changelog/tags/0-7-119", "page": 1, "postsPerPage": 20, "totalPages": 1, "totalCount": 1, "blogDescription": "Keep yourself up-to-date about new features in every release", "blogTitle": "PianoRhythm changelog"}}], "unlisted": false}, "/changelog/tags/0-7-91": {"inline": true, "label": "0.7.91", "permalink": "/changelog/tags/0-7-91", "items": ["/2022/12/07/0.7.91"], "pages": [{"items": ["/2022/12/07/0.7.91"], "metadata": {"permalink": "/changelog/tags/0-7-91", "page": 1, "postsPerPage": 20, "totalPages": 1, "totalCount": 1, "blogDescription": "Keep yourself up-to-date about new features in every release", "blogTitle": "PianoRhythm changelog"}}], "unlisted": false}}, "blogTagsListPath": "/changelog/tags"}}, "terminology-docusaurus-plugin": {}, "docusaurus-plugin-client-redirects": {}, "docusaurus-theme-mermaid": {}, "docusaurus-bootstrap-plugin": {}, "docusaurus-mdx-fallback-plugin": {}}}