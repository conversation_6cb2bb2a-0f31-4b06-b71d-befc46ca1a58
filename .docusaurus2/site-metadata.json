{"docusaurusVersion": "3.8.1", "siteVersion": "0.0.1", "pluginVersions": {"docusaurus-plugin-content-docs": {"type": "package", "name": "@docusaurus/plugin-content-docs", "version": "3.8.1"}, "docusaurus-plugin-content-blog": {"type": "package", "name": "@docusaurus/plugin-content-blog", "version": "3.8.1"}, "docusaurus-plugin-content-pages": {"type": "package", "name": "@docusaurus/plugin-content-pages", "version": "3.8.1"}, "docusaurus-plugin-google-gtag": {"type": "package", "name": "@docusaurus/plugin-google-gtag", "version": "3.8.1"}, "docusaurus-plugin-sitemap": {"type": "package", "name": "@docusaurus/plugin-sitemap", "version": "3.8.1"}, "docusaurus-plugin-svgr": {"type": "package", "name": "@docusaurus/plugin-svgr", "version": "3.8.1"}, "docusaurus-theme-classic": {"type": "package", "name": "@docusaurus/theme-classic", "version": "3.8.1"}, "docusaurus-tailwindcss": {"type": "local"}, "dev-webpack-configure": {"type": "project"}, "docusaurus-lunr-search": {"type": "package", "name": "docusaurus-lunr-search", "version": "3.6.0"}, "changelog-plugin": {"type": "project"}, "terminology-docusaurus-plugin": {"type": "package", "name": "@grnet/docusaurus-terminology", "version": "1.0.0-rc.2"}, "docusaurus-plugin-client-redirects": {"type": "package", "name": "@docusaurus/plugin-client-redirects", "version": "3.8.1"}, "docusaurus-theme-mermaid": {"type": "package", "name": "@docusaurus/theme-mermaid", "version": "3.8.1"}}}